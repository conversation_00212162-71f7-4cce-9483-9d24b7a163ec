import {
    initNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    formatNotificationMessage,
    formatNotificationTime
} from "/js/services/notificationService.js";

// DOM elements
let notificationIcon;
let notificationDropdown;
let notificationList;
let markAllReadButton;

// State
let isDropdownOpen = false;

/**
 * Initialize the notification UI
 */
export function initNotificationUI() {
    // Create notification icon if it doesn't exist
    createNotificationIcon();

    // Create notification dropdown if it doesn't exist
    createNotificationDropdown();

    // Add event listeners
    addEventListeners();

    // Initialize the notification service
    initNotifications(updateNotificationUI);
}

/**
 * Create the notification icon in the navbar
 */
function createNotificationIcon() {
    // Check if notification icon already exists
    notificationIcon = document.querySelector('.notification-icon');
    if (notificationIcon) return;

    // Create notification icon
    notificationIcon = document.createElement('div');
    notificationIcon.className = 'notification-icon';
    notificationIcon.setAttribute('aria-label', 'Notifications');

    // Insert in desktop navbar
    const desktopNavMenu = document.querySelector('.appbar .nav-menu');
    if (desktopNavMenu) {
        const profileButton = desktopNavMenu.querySelector('.profile-button');
        if (profileButton) {
            desktopNavMenu.insertBefore(notificationIcon, profileButton);
        } else {
            desktopNavMenu.appendChild(notificationIcon);
        }
    }

    // Insert in mobile navbar
    const mobileActions = document.querySelector('.mobile-actions');
    if (mobileActions) {
        const mobileNotificationIcon = notificationIcon.cloneNode(true);
        const profileIcon = mobileActions.querySelector('.profile-icon');
        if (profileIcon) {
            mobileActions.insertBefore(mobileNotificationIcon, profileIcon);
        } else {
            mobileActions.appendChild(mobileNotificationIcon);
        }
    }
}

/**
 * Create the notification dropdown
 */
function createNotificationDropdown() {
    // Check if notification dropdown already exists
    notificationDropdown = document.querySelector('.notification-dropdown');
    if (notificationDropdown) return;

    // Create notification dropdown
    notificationDropdown = document.createElement('div');
    notificationDropdown.className = 'notification-dropdown';

    // Create notification header
    const notificationHeader = document.createElement('div');
    notificationHeader.className = 'notification-header';

    const notificationTitle = document.createElement('div');
    notificationTitle.className = 'notification-title';
    notificationTitle.textContent = 'Notifications';

    notificationHeader.appendChild(notificationTitle);
    notificationDropdown.appendChild(notificationHeader);

    // Create notification list
    notificationList = document.createElement('ul');
    notificationList.className = 'notification-list';
    notificationDropdown.appendChild(notificationList);

    // Create notification footer
    const notificationFooter = document.createElement('div');
    notificationFooter.className = 'notification-footer';

    markAllReadButton = document.createElement('button');
    markAllReadButton.className = 'mark-all-read';
    markAllReadButton.textContent = 'Mark all as read';

    notificationFooter.appendChild(markAllReadButton);
    notificationDropdown.appendChild(notificationFooter);

    // Add to document
    document.body.appendChild(notificationDropdown);
}

/**
 * Add event listeners for notification interactions
 */
function addEventListeners() {
    // Toggle dropdown when notification icon is clicked
    document.addEventListener('click', (event) => {
        const clickedNotificationIcon = event.target.closest('.notification-icon');

        if (clickedNotificationIcon) {
            // Store which notification icon was clicked (mobile or desktop)
            // This helps with positioning the dropdown
            notificationIcon = clickedNotificationIcon;

            toggleNotificationDropdown();
            event.stopPropagation();
        } else if (!event.target.closest('.notification-dropdown') && isDropdownOpen) {
            closeNotificationDropdown();
        }
    });

    // Mark all as read button
    if (markAllReadButton) {
        markAllReadButton.addEventListener('click', async () => {
            const success = await markAllNotificationsAsRead();
            if (success) {
                // UI will be updated by the notification listener
                console.log('All notifications marked as read');
            }
        });
    }

    // Reposition dropdown on window resize
    window.addEventListener('resize', () => {
        if (isDropdownOpen) {
            positionNotificationDropdown();
        }
    });
}

/**
 * Toggle the notification dropdown
 */
function toggleNotificationDropdown() {
    if (isDropdownOpen) {
        closeNotificationDropdown();
    } else {
        openNotificationDropdown();
    }
}

/**
 * Open the notification dropdown
 */
function openNotificationDropdown() {
    if (!notificationDropdown) return;

    // Ensure the dropdown is properly positioned
    positionNotificationDropdown();

    notificationDropdown.classList.add('active');
    isDropdownOpen = true;
}

/**
 * Position the notification dropdown relative to the viewport
 */
function positionNotificationDropdown() {
    if (!notificationDropdown) return;

    // Make sure the dropdown is visible but not active yet for measuring
    notificationDropdown.style.display = 'flex';
    notificationDropdown.classList.remove('active');

    // Reset any previous inline styles
    notificationDropdown.style.top = '';
    notificationDropdown.style.right = '';
    notificationDropdown.style.left = '';
    notificationDropdown.style.transform = '';

    // Use the stored notification icon or find one if not available
    const iconElement = notificationIcon || document.querySelector('.notification-icon');
    if (iconElement) {
        const iconRect = iconElement.getBoundingClientRect();

        // Position the dropdown below the icon
        notificationDropdown.style.top = `${iconRect.bottom + 5}px`;

        // Determine if this is a mobile icon
        const isMobileIcon = iconElement.closest('.mobile-actions') !== null;

        // Position horizontally based on the icon's position and viewport
        if (window.innerWidth <= 768 || isMobileIcon) {
            // For mobile, center the dropdown under the icon that was clicked

            // Calculate the center position of the icon
            const iconCenter = iconRect.left + (iconRect.width / 2);

            // Use absolute positioning with transform for better centering
            notificationDropdown.style.left = `${iconCenter}px`;
            notificationDropdown.style.transform = `translateX(-50%)`;
            notificationDropdown.style.right = 'auto';

            // Check if the dropdown would go off-screen and adjust if needed
            const dropdownRect = notificationDropdown.getBoundingClientRect();
            if (dropdownRect.left < 10) {
                // Too far left, adjust position
                notificationDropdown.style.left = '10px';
                notificationDropdown.style.transform = 'none';
            } else if (dropdownRect.right > window.innerWidth - 10) {
                // Too far right, adjust position
                notificationDropdown.style.left = 'auto';
                notificationDropdown.style.right = '10px';
                notificationDropdown.style.transform = 'none';
            }
        } else {
            // For desktop, position relative to the icon
            // Center the dropdown under the icon
            notificationDropdown.style.left = `${iconRect.left + (iconRect.width / 2)}px`;
            notificationDropdown.style.transform = `translateX(-50%)`;
            notificationDropdown.style.right = 'auto';

            // Check if the dropdown would go off-screen and adjust if needed
            const dropdownRect = notificationDropdown.getBoundingClientRect();
            if (dropdownRect.left < 10) {
                // Too far left, adjust position
                notificationDropdown.style.left = '10px';
                notificationDropdown.style.transform = 'none';
            } else if (dropdownRect.right > window.innerWidth - 10) {
                // Too far right, adjust position
                notificationDropdown.style.left = 'auto';
                notificationDropdown.style.right = '10px';
                notificationDropdown.style.transform = 'none';
            }
        }
    } else {
        // Fallback positioning if no icon is found
        notificationDropdown.style.top = '60px';
        notificationDropdown.style.right = '20px';
        notificationDropdown.style.left = 'auto';
        notificationDropdown.style.transform = 'none';
    }

    // Reset display to none until it's activated
    notificationDropdown.style.display = '';
}

/**
 * Close the notification dropdown
 */
function closeNotificationDropdown() {
    if (!notificationDropdown) return;

    notificationDropdown.classList.remove('active');
    isDropdownOpen = false;
}

/**
 * Update the notification UI with new notifications
 * @param {Array} notifications - The notifications to display
 * @param {number} unreadCount - The number of unread notifications
 */
function updateNotificationUI(notifications, unreadCount) {
    // Update notification icon
    updateNotificationIcon(unreadCount);

    // Update notification list
    updateNotificationList(notifications);
}

/**
 * Update the notification icon based on unread count
 * @param {number} unreadCount - The number of unread notifications
 */
function updateNotificationIcon(unreadCount) {
    const notificationIcons = document.querySelectorAll('.notification-icon');

    notificationIcons.forEach(icon => {
        if (unreadCount > 0) {
            icon.classList.add('has-unread');
        } else {
            icon.classList.remove('has-unread');
        }
    });
}

/**
 * Update the notification list with new notifications
 * @param {Array} notifications - The notifications to display
 */
function updateNotificationList(notifications) {
    if (!notificationList) return;

    // Clear the list
    notificationList.innerHTML = '';

    // If no notifications, show a message
    if (!notifications || notifications.length === 0) {
        const noNotifications = document.createElement('li');
        noNotifications.className = 'no-notifications';
        noNotifications.textContent = 'No notifications';
        notificationList.appendChild(noNotifications);
        return;
    }

    // Add each notification to the list
    notifications.forEach(notification => {
        const notificationItem = createNotificationItem(notification);
        notificationList.appendChild(notificationItem);
    });
}

/**
 * Create a notification item element
 * @param {Object} notification - The notification data
 * @returns {HTMLElement} - The notification item element
 */
function createNotificationItem(notification) {
    const notificationItem = document.createElement('li');
    notificationItem.className = 'notification-item';
    notificationItem.dataset.id = notification.id;

    if (!notification.isRead) {
        notificationItem.classList.add('unread');
    }

    const notificationContent = document.createElement('div');
    notificationContent.className = 'notification-content';
    notificationContent.textContent = formatNotificationMessage(notification);

    const notificationTime = document.createElement('div');
    notificationTime.className = 'notification-time';
    notificationTime.textContent = formatNotificationTime(notification.createdAt);

    notificationItem.appendChild(notificationContent);
    notificationItem.appendChild(notificationTime);

    // Add click event to mark as read and navigate
    notificationItem.addEventListener('click', () => {
        handleNotificationClick(notification);
    });

    return notificationItem;
}

/**
 * Handle notification item click
 * @param {Object} notification - The notification that was clicked
 */
async function handleNotificationClick(notification) {
    // Mark notification as read
    if (!notification.isRead) {
        await markNotificationAsRead(notification.id);
    }

    // Close the dropdown
    closeNotificationDropdown();

    // Navigate based on notification type
    navigateToNotificationTarget(notification);
}

/**
 * Navigate to the target page based on notification type
 * @param {Object} notification - The notification to navigate to
 */
function navigateToNotificationTarget(notification) {
    // Get the question ID
    const questionId = notification.questionId;

    switch (notification.type) {
        case 'reply':
            // Navigate to the post with the reply
            if (questionId) {
                window.location.href = `/pages/forum/question.html?id=${questionId}`;
            }
            break;
        case 'mention':
            // Navigate to the post with the mention
            if (questionId) {
                window.location.href = `/pages/forum/question.html?id=${questionId}`;
            }
            break;
        case 'follow_post':
            // Navigate to the new post
            if (questionId) {
                window.location.href = `/pages/forum/question.html?id=${questionId}`;
            }
            break;
        default:
            // For any other type, try to navigate to the post if we have an ID
            if (questionId) {
                window.location.href = `/pages/forum/question.html?id=${questionId}`;
            } else {
                // Default navigation (e.g., to home page)
                console.log('No specific navigation for this notification type');
            }
    }
}
