/**
 * React Infinite Scroll Posts Component
 * Reusable React component for infinite scrolling forum posts
 * Can be used in React Native or React web applications
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';

const API_URL = 'https://api-epv42bk6oq-uc.a.run.app';

/**
 * Custom hook for infinite scroll functionality
 */
export const useInfiniteScrollPosts = (options = {}) => {
    const {
        apiEndpoint = `${API_URL}/api/posts`,
        limit = 15,
        onError = (error) => console.error('Error loading posts:', error)
    } = options;

    // State
    const [posts, setPosts] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [error, setError] = useState(null);

    // Refs
    const isMountedRef = useRef(true);

    // Load posts function
    const loadPosts = useCallback(async (page, isFirstPage = false) => {
        if (isLoading || (!hasMore && !isFirstPage)) {
            return;
        }

        try {
            setIsLoading(true);
            setError(null);

            const url = `${apiEndpoint}?page=${page}&limit=${limit}`;
            console.log(`Loading posts: ${url}`);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            // Validate response format
            if (!data.posts || !Array.isArray(data.posts)) {
                throw new Error('Invalid response format: posts array not found');
            }

            // Only update state if component is still mounted
            if (isMountedRef.current) {
                if (isFirstPage) {
                    setPosts(data.posts);
                } else {
                    setPosts(prevPosts => [...prevPosts, ...data.posts]);
                }

                setHasMore(data.hasMore !== false);
                setCurrentPage(page);

                console.log(`Loaded ${data.posts.length} posts. Page: ${page}, Has more: ${data.hasMore}`);
            }

        } catch (error) {
            console.error('Error loading posts:', error);
            
            if (isMountedRef.current) {
                setError(error.message);
                onError(error);
            }
        } finally {
            if (isMountedRef.current) {
                setIsLoading(false);
            }
        }
    }, [apiEndpoint, limit, isLoading, hasMore, onError]);

    // Load first page
    const loadFirstPage = useCallback(() => {
        setCurrentPage(1);
        setHasMore(true);
        setError(null);
        loadPosts(1, true);
    }, [loadPosts]);

    // Load next page
    const loadNextPage = useCallback(() => {
        if (!isLoading && hasMore) {
            loadPosts(currentPage + 1, false);
        }
    }, [loadPosts, currentPage, isLoading, hasMore]);

    // Refresh posts
    const refresh = useCallback(() => {
        loadFirstPage();
    }, [loadFirstPage]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            isMountedRef.current = false;
        };
    }, []);

    // Load first page on mount
    useEffect(() => {
        loadFirstPage();
    }, [loadFirstPage]);

    return {
        posts,
        isLoading,
        hasMore,
        error,
        currentPage,
        loadNextPage,
        refresh,
        loadFirstPage
    };
};

/**
 * Intersection Observer hook for detecting when to load more posts
 */
export const useIntersectionObserver = (callback, options = {}) => {
    const [element, setElement] = useState(null);
    const { threshold = 0.1, rootMargin = '200px' } = options;

    useEffect(() => {
        if (!element) return;

        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    callback();
                }
            },
            {
                threshold,
                rootMargin,
            }
        );

        observer.observe(element);

        return () => {
            observer.disconnect();
        };
    }, [element, callback, threshold, rootMargin]);

    return setElement;
};

/**
 * React component for infinite scroll posts
 */
export const InfiniteScrollPosts = ({
    renderPost,
    renderLoading = () => <div>Loading...</div>,
    renderError = (error) => <div>Error: {error}</div>,
    renderEmpty = () => <div>No posts available</div>,
    renderEndOfPosts = () => <div>You've reached the end!</div>,
    apiEndpoint,
    limit = 15,
    className = '',
    onError,
    ...props
}) => {
    const {
        posts,
        isLoading,
        hasMore,
        error,
        loadNextPage,
        refresh
    } = useInfiniteScrollPosts({
        apiEndpoint,
        limit,
        onError
    });

    // Set up intersection observer for loading more posts
    const sentinelRef = useIntersectionObserver(
        useCallback(() => {
            if (!isLoading && hasMore) {
                loadNextPage();
            }
        }, [isLoading, hasMore, loadNextPage]),
        { rootMargin: '200px' }
    );

    // Handle error state
    if (error && posts.length === 0) {
        return (
            <div className={`infinite-scroll-posts ${className}`} {...props}>
                {renderError(error)}
                <button onClick={refresh}>Retry</button>
            </div>
        );
    }

    // Handle empty state
    if (!isLoading && posts.length === 0) {
        return (
            <div className={`infinite-scroll-posts ${className}`} {...props}>
                {renderEmpty()}
            </div>
        );
    }

    return (
        <div className={`infinite-scroll-posts ${className}`} {...props}>
            {/* Render posts */}
            {posts.map((post, index) => (
                <div key={post.id || index} className="post-item">
                    {renderPost(post, index)}
                </div>
            ))}

            {/* Loading indicator */}
            {isLoading && renderLoading()}

            {/* End of posts message */}
            {!hasMore && posts.length > 0 && renderEndOfPosts()}

            {/* Intersection observer sentinel */}
            {hasMore && (
                <div
                    ref={sentinelRef}
                    style={{
                        height: '1px',
                        visibility: 'hidden',
                        pointerEvents: 'none'
                    }}
                />
            )}
        </div>
    );
};

/**
 * Higher-order component for adding infinite scroll to any component
 */
export const withInfiniteScroll = (WrappedComponent) => {
    return function InfiniteScrollWrapper(props) {
        const infiniteScrollProps = useInfiniteScrollPosts(props);

        return (
            <WrappedComponent
                {...props}
                {...infiniteScrollProps}
            />
        );
    };
};

/**
 * React Native compatible version
 * Uses FlatList with onEndReached instead of IntersectionObserver
 */
export const InfiniteScrollPostsRN = ({
    renderPost,
    renderLoading,
    renderError,
    renderEmpty,
    apiEndpoint,
    limit = 15,
    onError,
    ...flatListProps
}) => {
    const {
        posts,
        isLoading,
        hasMore,
        error,
        loadNextPage,
        refresh
    } = useInfiniteScrollPosts({
        apiEndpoint,
        limit,
        onError
    });

    // Handle loading more posts
    const handleEndReached = useCallback(() => {
        if (!isLoading && hasMore) {
            loadNextPage();
        }
    }, [isLoading, hasMore, loadNextPage]);

    // Render item function for FlatList
    const renderItem = useCallback(({ item, index }) => (
        renderPost(item, index)
    ), [renderPost]);

    // Key extractor for FlatList
    const keyExtractor = useCallback((item, index) => 
        item.id || index.toString()
    , []);

    // Footer component for loading indicator
    const ListFooterComponent = useCallback(() => {
        if (isLoading) return renderLoading ? renderLoading() : null;
        if (!hasMore && posts.length > 0) return <div>You've reached the end!</div>;
        return null;
    }, [isLoading, hasMore, posts.length, renderLoading]);

    // Handle error state
    if (error && posts.length === 0) {
        return renderError ? renderError(error) : <div>Error: {error}</div>;
    }

    // Note: This would use React Native's FlatList in a real RN app
    // For now, we'll return a placeholder that shows the concept
    return (
        <div className="infinite-scroll-posts-rn">
            {/* In React Native, this would be:
            <FlatList
                data={posts}
                renderItem={renderItem}
                keyExtractor={keyExtractor}
                onEndReached={handleEndReached}
                onEndReachedThreshold={0.1}
                ListFooterComponent={ListFooterComponent}
                ListEmptyComponent={renderEmpty}
                onRefresh={refresh}
                refreshing={isLoading}
                {...flatListProps}
            />
            */}
            <div>React Native FlatList would be used here</div>
        </div>
    );
};

export default InfiniteScrollPosts;
