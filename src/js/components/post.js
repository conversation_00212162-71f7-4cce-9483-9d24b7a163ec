
async function fetchUsernameById(userId) {
    try {
        // Always use the production API
        const apiUrl = 'https://api-epv42bk6oq-uc.a.run.app';

        try {
            const response = await fetch(`${apiUrl}/api/users/username/${userId}`);

            if (response.ok) {
                const userData = await response.json();
                return userData.username;
            }

            throw new Error(`HTTP error! status: ${response.status}`);
        } catch (fetchError) {
            console.error(`Error fetching username for userId ${userId}:`, fetchError);
            return null;
        }
    } catch (error) {
        console.error(`Error in fetchUsernameById for userId ${userId}:`, error);
        return null;
    }
}

/**
 * Fetch reply count for a post
 * @param {string} postId - The ID of the post to fetch replies for
 * @returns {Promise<number>} The number of replies or 0 if none found
 */
async function fetchReplyCount(postId) {
    try {
        // Always use the production API
        const apiUrl = 'https://api-epv42bk6oq-uc.a.run.app';

        try {
            const response = await fetch(`${apiUrl}/api/posts/${postId}/replies`);

            if (response.ok) {
                const data = await response.json();
                return Array.isArray(data) ? data.length : 0;
            }

            throw new Error(`HTTP error! status: ${response.status}`);
        } catch (fetchError) {
            console.error(`Error fetching replies for postId ${postId}:`, fetchError);
            return 0;
        }
    } catch (error) {
        console.error(`Error in fetchReplyCount for postId ${postId}:`, error);
        return 0;
    }
}


async function createPostComponent(question, detailPageUrl = '/pages/forum/question.html') {
    try {
        // Fetch the post template
        const response = await fetch('/components/post.html');
        const html = await response.text();
        // Create a temporary container to hold the template
        const container = document.createElement('div');
        container.innerHTML = html.trim();

        // Get the post element
        const postElement = container.firstChild;

        // Set post data
        // Title
        postElement.querySelector('#post-title').textContent = question.title || 'Untitled Question';

        // Username
        const usernameElement = postElement.querySelector('#post-username');

        // If username is already available in the question object (should be preloaded)
        if (question.username) {
            // Make the username clickable
            usernameElement.innerHTML = `<a href="/pages/profile/profile.html?userId=${question.userId}" class="username-link">${question.username}</a>`;
        }
        // If we have userId but no username (fallback case)
        else if (question.userId) {
            // Use a shortened userId as fallback
            usernameElement.textContent = `User ${question.userId.substring(0, 6)}`;
        }
        // Fallback if no userId is available (should not happen based on your requirements)
        else {
            usernameElement.textContent = 'Unknown User';
        }

        // Date
        const postDate = postElement.querySelector('#post-date');
        if (question.createdAt && typeof question.createdAt === 'object') {
            let date;

            // Handle different date formats
            if (question.createdAt._seconds && question.createdAt._nanoseconds) {
                // Firestore Timestamp format from JSON with underscore prefix
                date = new Date(question.createdAt._seconds * 1000);
            } else if (question.createdAt.seconds && question.createdAt.nanoseconds) {
                // Firestore Timestamp
                date = new Date(question.createdAt.seconds * 1000);
            } else if (question.createdAt.toDate && typeof question.createdAt.toDate === 'function') {
                // Handle Firestore Timestamp object with toDate method
                date = question.createdAt.toDate();
            } else if (typeof question.createdAt === 'string') {
                // String date format (e.g., "April 23, 2025 at 8:49:16PM UTC+3")
                date = new Date(question.createdAt);
            } else if (typeof question.createdAt === 'number') {
                // Timestamp in milliseconds
                date = new Date(question.createdAt);
            } else {
                // Try to convert directly
                try {
                    // If it's an object with a timestamp-like structure
                    if (typeof question.createdAt === 'object') {
                        // Try to extract any timestamp-like values
                        const possibleTimestamp =
                            question.createdAt.timestamp ||
                            question.createdAt.date ||
                            question.createdAt.time ||
                            (question.createdAt.seconds ? question.createdAt.seconds * 1000 : null);

                        if (possibleTimestamp) {
                            date = new Date(possibleTimestamp);
                        } else {
                            // Last resort: use current date
                            date = new Date();
                            console.warn('Using current date as fallback');
                        }
                    } else {
                        date = new Date(question.createdAt);
                    }
                } catch (e) {
                    // Fallback to current date without verbose logging
                    date = new Date();
                }
            }

            // Check if date is valid
            if (!isNaN(date.getTime())) {
                postDate.textContent = `${date.toLocaleDateString()}`;
            }
        } else {
            postDate.textContent = 'asked on unknown date';
        }

        // Tags
        const tagsContainer = postElement.querySelector('#post-tags');
        if (question.tags) {
            // Handle different tag formats
            let tags = [];

            if (Array.isArray(question.tags)) {
                // Standard array format
                tags = question.tags;
            } else if (typeof question.tags === 'object' && !Array.isArray(question.tags)) {
                // Handle Firestore map format with numeric keys (e.g., {0: "tag1", 1: "tag2"})
                tags = Object.values(question.tags);
            }

            // Add tags to the container
            tags.forEach(tag => {
                // Skip empty tags
                if (!tag) return;

                // Handle tag objects or strings
                const tagText = typeof tag === 'object' ? (tag.text || tag.name || '') : tag;

                if (tagText) {
                    const tagElement = document.createElement('span');
                    tagElement.className = 'tag';
                    tagElement.textContent = tagText;
                    tagsContainer.appendChild(tagElement);
                }
            });
        }

        // Fetch and display reply count
        const repliesElement = postElement.querySelector('#post-replies');
        if (repliesElement && question.id) {
            // Set initial loading state
            repliesElement.textContent = 'Loading replies...';

            // Fetch reply count
            fetchReplyCount(question.id)
                .then(count => {
                    // Update the reply count text
                    repliesElement.textContent = count === 1 ? '1 reply' : `${count} replies`;
                })
                .catch(() => {
                    // Set default text if fetching fails
                    repliesElement.textContent = '0 replies';
                });
        }

        // Make the post clickable to view details
        postElement.addEventListener('click', (event) => {
            // Don't navigate if clicking on a link (like the username)
            if (event.target.tagName === 'A' || event.target.closest('a')) {
                // Stop event propagation to prevent post click
                event.stopPropagation();
                return;
            }

            // Navigate to question detail page
            window.location.href = `${detailPageUrl}?id=${question.id}`;
        });

        return postElement;
    } catch (error) {
        // Create a simple fallback element without verbose logging
        const fallbackElement = document.createElement('div');
        fallbackElement.className = 'post-fallback';
        fallbackElement.textContent = 'Error loading question';

        return fallbackElement;
    }
}

async function renderPosts(questions, container, detailPageUrl = '/pages/forum/question.html', showLoadingIndicator = true) {
    // Clear the container
    container.innerHTML = '';

    // Add a loading indicator if requested
    let loadingIndicator;
    if (showLoadingIndicator) {
        loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'posts-loading';
        loadingIndicator.textContent = 'Loading posts...';
        container.appendChild(loadingIndicator);
    }

    // If no questions, show a message
    if (!questions || questions.length === 0) {
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
        const message = document.createElement('p');
        message.textContent = 'No questions found.';
        container.appendChild(message);
        return;
    }

    // Sort questions by creation date (newest first)
    try {
        questions.sort((a, b) => {
            // Helper function to convert various date formats to Date objects
            const parseDate = (dateInput) => {
                if (!dateInput || typeof dateInput !== 'object') return new Date(0);

                if (dateInput._seconds && dateInput._nanoseconds) {
                    // Firestore Timestamp format from JSON with underscore prefix
                    return new Date(dateInput._seconds * 1000);
                } else if (dateInput.seconds && dateInput.nanoseconds) {
                    // Firestore Timestamp
                    return new Date(dateInput.seconds * 1000);
                } else if (dateInput.toDate && typeof dateInput.toDate === 'function') {
                    // Handle Firestore Timestamp object with toDate method
                    return dateInput.toDate();
                } else if (typeof dateInput === 'string') {
                    // String date format
                    return new Date(dateInput);
                } else if (typeof dateInput === 'number') {
                    // Timestamp in milliseconds
                    return new Date(dateInput);
                } else {
                    // Try to convert directly
                    try {
                        // If it's an object with a timestamp-like structure
                        if (typeof dateInput === 'object') {
                            // Try to extract any timestamp-like values
                            const possibleTimestamp =
                                dateInput.timestamp ||
                                dateInput.date ||
                                dateInput.time ||
                                (dateInput.seconds ? dateInput.seconds * 1000 : null);

                            if (possibleTimestamp) {
                                return new Date(possibleTimestamp);
                            }
                        }

                        const date = new Date(dateInput);
                        return isNaN(date.getTime()) ? new Date(0) : date;
                    } catch (e) {
                        // Return default date without verbose logging
                        return new Date(0);
                    }
                }
            };

            try {
                const dateA = parseDate(a.createdAt);
                const dateB = parseDate(b.createdAt);
                return dateB - dateA;
            } catch (error) {
                // Keep original order if comparison fails
                return 0;
            }
        });
    } catch (sortError) {
        // Continue without sorting if there's an error
    }

    // Preload all usernames before rendering posts
    try {
        // Create a map to store userIds that need username fetching
        const userIdsToFetch = new Map();

        // Collect all userIds that don't have usernames
        for (const question of questions) {
            if (question.userId && !question.username) {
                userIdsToFetch.set(question.userId, question);
            }
        }

        // If there are userIds to fetch, fetch them all in parallel
        if (userIdsToFetch.size > 0) {
            console.log(`Preloading ${userIdsToFetch.size} usernames...`);

            const userIds = Array.from(userIdsToFetch.keys());
            const fetchPromises = userIds.map(userId => fetchUsernameById(userId));

            // Wait for all username fetches to complete
            const usernames = await Promise.all(fetchPromises);

            // Assign usernames to questions
            userIds.forEach((userId, index) => {
                const username = usernames[index];
                if (username) {
                    // Update all questions with this userId
                    for (const question of questions) {
                        if (question.userId === userId) {
                            question.username = username;
                        }
                    }
                }
            });

            console.log('All usernames preloaded');
        }
    } catch (error) {
        console.error('Error preloading usernames:', error);
        // Continue with rendering even if username preloading fails
    }

    // Remove loading indicator if it exists
    if (loadingIndicator) {
        loadingIndicator.remove();
    }

    // Create a temporary container to build all posts
    const tempContainer = document.createDocumentFragment();

    // Create all post elements
    const postElements = await Promise.all(
        questions.map(question => createPostComponent(question, detailPageUrl))
    );

    // Add all post elements to the temporary container
    for (const postElement of postElements) {
        if (postElement) {
            tempContainer.appendChild(postElement);
        }
    }

    // Add all posts to the actual container at once
    container.appendChild(tempContainer);
}

export { createPostComponent, renderPosts };
