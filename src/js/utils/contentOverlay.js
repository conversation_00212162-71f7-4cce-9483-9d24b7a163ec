import { auth } from "/js/core/firebase_config.js";
import { onAuthStateChanged } from "https://www.gstatic.com/firebasejs/9.21.0/firebase-auth.js";

/**
 * Content Preview Overlay for Anonymous Users
 * Encourages registration by showing a preview with call-to-action
 */

class ContentOverlay {
    constructor() {
        this.overlay = null;
        this.isAuthenticated = false;
        this.authInitialized = false;
        this.postsContainer = null;
        this.visiblePostsCount = 3; // Show top 3 posts clearly
    }

    /**
     * Initialize the overlay system
     * @param {string} postsContainerSelector - CSS selector for posts container
     * @param {Object} options - Configuration options
     */
    init(postsContainerSelector = '.questions-list', options = {}) {
        // Prevent duplicate initialization
        if (this.authInitialized) {
            console.log('ContentOverlay: Already initialized, skipping');
            return;
        }

        this.postsContainer = document.querySelector(postsContainerSelector);

        if (!this.postsContainer) {
            console.warn('ContentOverlay: Posts container not found');
            return;
        }

        console.log('ContentOverlay: Initializing with container:', postsContainerSelector);

        // Set up authentication state listener first
        this.setupAuthListener();

        // Check initial auth state
        const currentUser = auth.currentUser;
        console.log('ContentOverlay: Initial auth check', {
            currentUser: !!currentUser,
            uid: currentUser ? currentUser.uid : null
        });

        // Only handle initial state if we have a definitive auth state
        // Otherwise, wait for the onAuthStateChanged callback
        if (currentUser !== undefined) {
            this.handleAuthState(currentUser);
        }
    }

    /**
     * Set up Firebase auth state listener
     */
    setupAuthListener() {
        onAuthStateChanged(auth, (user) => {
            console.log('ContentOverlay: Firebase auth state changed', {
                user: !!user,
                uid: user ? user.uid : null,
                emailVerified: user ? user.emailVerified : null,
                isAnonymous: user ? user.isAnonymous : null
            });
            this.handleAuthState(user);
        });
    }

    /**
     * Handle authentication state changes
     * @param {Object|null} user - Firebase user object
     */
    handleAuthState(user) {
        const wasAuthenticated = this.isAuthenticated;
        // Match the authentication logic used in navbar.js and profile.js - just check if user exists
        this.isAuthenticated = !!user;
        this.authInitialized = true;

        console.log('ContentOverlay: Auth state changed', {
            user: !!user,
            uid: user ? user.uid : null,
            emailVerified: user ? user.emailVerified : false,
            isAuthenticated: this.isAuthenticated,
            wasAuthenticated: wasAuthenticated
        });

        // Update body class for styling
        document.body.classList.toggle('authenticated', this.isAuthenticated);
        document.body.classList.toggle('anonymous-mode', !this.isAuthenticated);

        if (this.isAuthenticated) {
            console.log('ContentOverlay: User is authenticated, hiding overlay');
            this.hideOverlay();
            this.enablePostInteractions();
        } else {
            console.log('ContentOverlay: User is anonymous, showing overlay');
            this.showOverlay();
            this.disablePostInteractions();
        }

        // If auth state changed from unauthenticated to authenticated, refresh content
        if (!wasAuthenticated && this.isAuthenticated) {
            this.refreshContent();
        }
    }

    /**
     * Create and show the overlay
     */
    showOverlay() {
        if (this.overlay) {
            this.overlay.classList.add('show', 'active');
            document.body.classList.add('overlay-active');
            return;
        }

        this.overlay = this.createOverlayElement();
        document.body.appendChild(this.overlay);
        document.body.classList.add('overlay-active');

        // Trigger animation after a brief delay
        setTimeout(() => {
            this.overlay.classList.add('show', 'active');
        }, 100);

        this.applyContentEffects();
    }

    /**
     * Hide the overlay
     */
    hideOverlay() {
        document.body.classList.remove('overlay-active');

        if (this.overlay) {
            this.overlay.classList.remove('show', 'active');
            setTimeout(() => {
                if (this.overlay && this.overlay.parentNode) {
                    this.overlay.parentNode.removeChild(this.overlay);
                    this.overlay = null;
                }
            }, 500);
        }
        this.removeContentEffects();
    }

    /**
     * Create the overlay DOM element
     * @returns {HTMLElement} The overlay element
     */
    createOverlayElement() {
        const overlay = document.createElement('div');
        overlay.className = 'content-preview-overlay';

        // Determine the title based on current page
        const currentPath = window.location.pathname;
        let title = "Join MetuHub Community";

        if (currentPath.includes('/question.html')) {
            title = "Want to Read More?";
        } else if (currentPath.includes('/profile.html')) {
            title = "Discover More Questions";
        }

        overlay.innerHTML = `
            <div class="overlay-content">
                <h2 class="overlay-title">${title}</h2>
                <a href="/pages/auth/login.html" class="overlay-cta-button">
                    Sign Up / Login
                </a>
            </div>
        `;

        // Add click handler for CTA button
        const ctaButton = overlay.querySelector('.overlay-cta-button');
        ctaButton.addEventListener('click', (e) => {
            e.preventDefault();
            window.location.href = '/pages/auth/login.html';
        });

        return overlay;
    }

    /**
     * Apply visual effects to content (blur, fade)
     */
    applyContentEffects() {
        if (!this.postsContainer) return;

        // Handle different page types
        const posts = this.postsContainer.querySelectorAll('.post-item, .question-container');

        // For question detail pages, blur the entire content
        if (this.postsContainer.classList.contains('question-container')) {
            this.postsContainer.classList.add('content-blurred');
        } else {
            // For forum and profile pages, blur posts after the visible count
            posts.forEach((post, index) => {
                if (index >= this.visiblePostsCount) {
                    post.classList.add('content-blurred');
                }
            });
        }

        // Add fade mask to container
        this.postsContainer.classList.add('content-fade-mask');
    }

    /**
     * Remove visual effects from content
     */
    removeContentEffects() {
        if (!this.postsContainer) return;

        const posts = this.postsContainer.querySelectorAll('.post-item, .question-container');
        posts.forEach(post => {
            post.classList.remove('content-blurred');
        });

        // Also remove blur from the container itself (for question pages)
        this.postsContainer.classList.remove('content-blurred');
        this.postsContainer.classList.remove('content-fade-mask');
    }

    /**
     * Disable post interactions for anonymous users
     */
    disablePostInteractions() {
        if (!this.postsContainer) return;

        const posts = this.postsContainer.querySelectorAll('.post-item');
        posts.forEach(post => {
            // Remove existing click listeners by cloning the element
            const newPost = post.cloneNode(true);
            post.parentNode.replaceChild(newPost, post);

            // Add click handler that redirects to login
            newPost.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                window.location.href = '/pages/auth/login.html';
            });
        });

        // Disable username links
        const usernameLinks = this.postsContainer.querySelectorAll('.username-link');
        usernameLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                window.location.href = '/pages/auth/login.html';
            });
        });
    }

    /**
     * Enable post interactions for authenticated users
     */
    enablePostInteractions() {
        // This will be handled by the original post rendering logic
        // We just need to remove the anonymous-mode class
        document.body.classList.remove('anonymous-mode');
    }

    /**
     * Refresh content after authentication
     */
    refreshContent() {
        // This can be overridden by specific page implementations
        // to reload content after authentication
        console.log('ContentOverlay: User authenticated, content can be refreshed');
    }

    /**
     * Destroy the overlay and clean up
     */
    destroy() {
        this.hideOverlay();
        this.removeContentEffects();
        this.enablePostInteractions();
        document.body.classList.remove('authenticated', 'anonymous-mode', 'overlay-active');
    }
}

// Create a singleton instance
const contentOverlay = new ContentOverlay();

// Export for use in other modules
export { contentOverlay, ContentOverlay };

// Auto-initialize on DOM content loaded if not already initialized
document.addEventListener('DOMContentLoaded', () => {
    if (!contentOverlay.authInitialized) {
        contentOverlay.init();
    }
});
