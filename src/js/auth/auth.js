const API_URL = 'https://api-epv42bk6oq-uc.a.run.app';

async function signup(email, password, username) {
    try {
        const response = await fetch(`${API_URL}/api/auth/signup`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password, username })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Signup failed');
        }

        const data = await response.json();
        return data;
    } catch (error) {
        throw error;
    }
}

export {
    signup
};