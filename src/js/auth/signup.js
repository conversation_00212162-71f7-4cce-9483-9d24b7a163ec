import { getAuth, createUserWithEmailAndPassword, sendEmailVerification } from "https://www.gstatic.com/firebasejs/9.21.0/firebase-auth.js";
import { app } from "../core/firebase_config.js";

const auth = getAuth(app);

function validateMetuEmail(email) {
    return email.endsWith('@metu.edu.tr');
}

function validatePassword(password) {
    return password.length >= 6;
}

function setupFormValidation() {
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const signupButton = document.getElementById('signupButton');
    const emailError = document.getElementById('emailError');
    const passwordError = document.getElementById('passwordError');
    const confirmPasswordError = document.getElementById('confirmPasswordError');

    emailInput.addEventListener('input', () => {
        if (!validateMetuEmail(emailInput.value)) {
            emailError.textContent = 'Please use a valid METU email address';
            emailInput.classList.add('error');
        } else {
            emailError.textContent = '';
            emailInput.classList.remove('error');
        }
    });

    passwordInput.addEventListener('input', () => {
        if (!validatePassword(passwordInput.value)) {
            passwordError.textContent = 'Password must be at least 6 characters';
            passwordInput.classList.add('error');
        } else {
            passwordError.textContent = '';
            passwordInput.classList.remove('error');
        }
    });

    confirmPasswordInput.addEventListener('input', () => {
        if (confirmPasswordInput.value !== passwordInput.value) {
            confirmPasswordError.textContent = 'Passwords do not match';
            confirmPasswordInput.classList.add('error');
        } else {
            confirmPasswordError.textContent = '';
            confirmPasswordInput.classList.remove('error');
        }
    });
}

function setupSignupForm() {
    const form = document.getElementById('signupForm');
    const verificationSection = document.querySelector('.verification-section');
    const signupButton = document.getElementById('signupButton');

    signupButton.addEventListener('click', async (e) => {
        e.preventDefault();
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // Clear previous error states
        const emailError = document.getElementById('emailError');
        const passwordError = document.getElementById('passwordError');
        const confirmPasswordError = document.getElementById('confirmPasswordError');
        emailError.textContent = '';
        passwordError.textContent = '';
        confirmPasswordError.textContent = '';

        // Validate inputs
        let hasError = false;

        if (!validateMetuEmail(email)) {
            emailError.textContent = 'Please use a valid METU email address';
            hasError = true;
        }

        if (!validatePassword(password)) {
            passwordError.textContent = 'Password must be at least 6 characters';
            hasError = true;
        }

        if (password !== confirmPassword) {
            confirmPasswordError.textContent = 'Passwords do not match';
            hasError = true;
        }

        if (hasError) {
            return;
        }

        try {
            signupButton.disabled = true;
            signupButton.textContent = 'Signing up...';
            
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            await sendEmailVerification(userCredential.user);
            
            form.style.display = 'none';
            verificationSection.style.display = 'block';
        } catch (error) {
            console.error('Error during signup:', error);
            let errorMessage = 'An error occurred during signup. Please try again.';
            
            if (error.code === 'auth/email-already-in-use') {
                errorMessage = 'This email is already registered.';
            }
            
            emailError.textContent = errorMessage;
        } finally {
            signupButton.disabled = false;
            signupButton.textContent = 'Sign Up';
        }
    });

}

function setupVerificationRedirect() {
    const user = auth.currentUser;
    if (user && !user.emailVerified) {
        window.location.href = '/verification.html';
    }
}


document.addEventListener('DOMContentLoaded', () => {
    setupFormValidation();
    setupSignupForm();
    setupVerificationCode();
});