import { getAuth, sendEmailVerification, signOut } from "https://www.gstatic.com/firebasejs/9.21.0/firebase-auth.js";
import { app } from "../core/firebase_config.js";

const auth = getAuth(app);

function setupVerificationHandlers() {
    const resendButton = document.getElementById('resendVerification');
    const checkButton = document.getElementById('checkVerification');
    const logoutButton = document.getElementById('logoutButton');
    const verificationError = document.getElementById('verificationError');

    // Check if user is already verified
    const user = auth.currentUser;
    if (user && user.emailVerified) {
        window.location.href = '/index.html';
        return;
    }

    if (resendButton) {
        resendButton.addEventListener('click', async () => {
            try {
                resendButton.disabled = true;
                const user = auth.currentUser;
                if (user) {
                    await sendEmailVerification(user);
                    resendButton.textContent = 'Verification email sent!';
                    setTimeout(() => {
                        resendButton.textContent = 'Resend verification email';
                        resendButton.disabled = false;
                    }, 5000);
                }
            } catch (error) {
                console.error('Error sending verification email:', error);
                verificationError.textContent = 'Failed to send verification email. Please try again.';
                resendButton.disabled = false;
            }
        });
    }

    if (checkButton) {
        checkButton.addEventListener('click', async () => {
            try {
                checkButton.disabled = true;
                checkButton.textContent = 'Checking...';
                
                const user = auth.currentUser;
                if (!user) {
                    throw new Error('No user found');
                }

                await user.reload();
                if (user.emailVerified) {
                    window.location.href = '/main.html';
                } else {
                    verificationError.textContent = 'Email not verified yet. Please check your email and click the verification link.';
                }
            } catch (error) {
                console.error('Verification check error:', error);
                verificationError.textContent = 'Failed to check verification status. Please try again.';
            } finally {
                checkButton.disabled = false;
                checkButton.textContent = "I've verified my email";
            }
        });
    }

    if (logoutButton) {
        logoutButton.addEventListener('click', async () => {
            try {
                await signOut(auth);
                window.location.href = '/index.html';
            } catch (error) {
                console.error('Logout error:', error);
                verificationError.textContent = 'Failed to logout. Please try again.';
            }
        });
    }
}

document.addEventListener('DOMContentLoaded', () => {
    setupVerificationHandlers();
});