import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/9.21.0/firebase-auth.js";
import { app } from "../core/firebase_config.js";

const auth = getAuth(app);

function redirectToVerification() {
    window.location.href = '/index.html#verification';
}

function redirectToLogin() {
    window.location.href = '/index.html';
}

function isProtectedRoute() {
    const protectedPaths = [
        '/main.html',
        '/hoca-database'
    ];
    return protectedPaths.some(path => window.location.pathname.includes(path));
}

function initAuthStateListener() {
    onAuthStateChanged(auth, (user) => {
        if (isProtectedRoute()) {
            if (!user) {
                redirectToLogin();
            } else if (!user.emailVerified) {
                redirectToVerification();
            }
        } else if (user && user.emailVerified && window.location.pathname === '/index.html') {
            window.location.href = '/main.html';
        }
    });
}

document.addEventListener('DOMContentLoaded', () => {
    initAuthStateListener();
});