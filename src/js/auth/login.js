import { getAuth, signInWithEmailAndPassword } from "https://www.gstatic.com/firebasejs/9.21.0/firebase-auth.js";
import { app } from "../core/firebase_config.js";

const auth = getAuth(app);

function setupLoginForm() {
    const loginForm = document.getElementById('loginForm');
    const loginButton = document.getElementById('loginButton');
    const loginError = document.getElementById('loginError');

    if (!loginForm || !loginButton) return;

    loginButton.addEventListener('click', async (e) => {
        e.preventDefault();
        const email = document.getElementById('loginEmail').value;
        const password = document.getElementById('loginPassword').value;

        try {
            loginButton.disabled = true;
            loginButton.textContent = 'Logging in...';
            
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;

            if (!user.emailVerified) {
                window.location.href = '/index.html#verification';
            } else {
                window.location.href = '/main.html';
            }
        } catch (error) {
            console.error('Login error:', error);
            let errorMessage = 'Invalid email or password';
            
            if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
                errorMessage = 'Invalid email or password';
            } else if (error.code === 'auth/too-many-requests') {
                errorMessage = 'Too many failed attempts. Please try again later.';
            }
            
            if (loginError) {
                loginError.textContent = errorMessage;
            }
        } finally {
            loginButton.disabled = false;
            loginButton.textContent = 'Login';
        }
    });
}

document.addEventListener('DOMContentLoaded', () => {
    setupLoginForm();
});