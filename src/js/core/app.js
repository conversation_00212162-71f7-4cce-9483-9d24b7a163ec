import { addRating } from "../services/submitRating.js";
import { getMeals } from "../fetch_meal_data.js";
import { signInAnonymouslyWithFirebase } from "./authentication/authentication.js";
import { fetchHistogramData, updateHistogramBars, calculateAverage } from "../fetch_data.js";

// Butonu seç
const submitButton = document.querySelector(".publish");

// Kullanıcı puanını seçmek için bir değişken oluşturun (örneğin, yıldızların seçimi)
let selectedRating = null;
let isLoading = true;

function updateUI() {
    const loadingScreen = document.getElementById('loading-screen');
    const reviewContainer = document.getElementById('reviewField');
    if (isLoading) {
        reviewContainer.style.display = 'none'; 
        loadingScreen.style.display = 'flex';
    } else {
        reviewContainer.classList.remove('hidden');
        loadingScreen.style.display = 'none';
    }
}

// Yıldızlara tıklanma işlevselliğini tanımlayın
document.querySelectorAll(".star").forEach((star) => {
    star.addEventListener("click", () => {
        selectedRating = parseInt(star.getAttribute("data-index"));
    });
});

// Butona tıklanma işlevselliğini tanımlayın
submitButton.addEventListener("click", async () => {
    if (selectedRating === null) {
        alert("Lütfen bir puan seçin!");
        return;
    }

    const result = await addRating(selectedRating);
    
    if (result.success) {
        console.log("Voted successfully!");
        submitButton.textContent = "Oyunuz kaydedildi!";
    } else {
        alert(result.message);
    }
});

// Uygulama başlatma işlemleri
async function initializeApp() {
    try {
        // Firebase'e anonim giriş
        signInAnonymouslyWithFirebase();

        // Yemek verilerini çek
        await getMeals();

        // Histogram verilerini çek
        const count = await fetchHistogramData();
        if (count) {
            updateHistogramBars(count);
            calculateAverage(count);
        }
    } catch (error) {
        console.error("Veriler yüklenirken hata oluştu:", error);
    } finally {
        // Yüklenme durumunu false yap
        isLoading = false;
        updateUI();
    }
}

// Sayfa tamamen yüklendiğinde başlat
document.addEventListener("DOMContentLoaded", () => {
    initializeApp();
});
