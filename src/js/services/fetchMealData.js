import { db } from "./core/firebase_config.js";
import { doc, getDoc } from "./core/firebase_config.js";

export async function getMeals() {
    const docRef = doc(db, "meals", "cafeteria-meals");
    const docSnapshot = await getDoc(docRef);

    if (docSnapshot.exists()) {
        const data = docSnapshot.data();
        const mealsDiv = document.getElementById("meals");

        // Verinin doğru formatta olduğundan emin olalım (Veri "meals" içinde doğrudan olmalı)
        const meals = data.meals;

        // "meals" içindeki yemek verisini döngü ile gösterme
        for (const [mealName, imageUrl] of Object.entries(meals)) {
            const itemDiv = document.createElement("div");
            itemDiv.className = "meal-item";

            const img = document.createElement("img");

            img.src = imageUrl;
            img.alt = mealName;

            const caption = document.createElement("p");
            caption.textContent = mealName;

            itemDiv.appendChild(img);
            itemDiv.appendChild(caption);
            mealsDiv.appendChild(itemDiv);

            console.log("Yemek:", mealName, "URL:", img.src);
        }
    } else {
        console.log("Doküman bulunamadı!");
    }
}