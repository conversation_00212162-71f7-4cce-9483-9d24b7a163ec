import { getAuth } from "https://www.gstatic.com/firebasejs/9.21.0/firebase-auth.js";

// Add rating to Firestore
export async function addRating(rating) {
    try {
        const auth = getAuth();
        const user = auth.currentUser;
        const userId = user.uid;

        if (!user) {
            throw new Error("The user is not authenticated.");
        }

        const ratingData = {
            user: userId,
            rating: rating,
        };

        const url = 'https://api-epv42bk6oq-uc.a.run.app';

        const response = await fetch(`${url}/submit/add-rating`, {
          method: 'POST',
          body: JSON.stringify(ratingData),
          headers: {
            'Content-Type': 'application/json'
          }
        });

        console.log('Yanıt geliyor.');

        if (!response.ok) {
            // Hata durumunda sunucudan dönen mesajı al
            const errorData = await response.json();
            console.error('Error Message:', errorData.error);

            // <PERSON><PERSON>en kullanıcıya mesaj g<PERSON> (alert veya bir UI bileşeni)
            alert(errorData.error);
            return { success: false, message: errorData.error };
        }

        return { success: true };
    } catch (error) {
        console.error(error);
        return { success: false, message: "The rate can not send to service." };
    }
}
