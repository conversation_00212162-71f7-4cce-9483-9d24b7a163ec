import { db, doc, getDoc } from "./core/firebase_config.js";
import { formatDate } from "./currentDate.js";

export async function fetchHistogramData() {
    const formattedDate = formatDate(new Date());
    const docRef = doc(db, "daily-ratings", formattedDate);

    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
        const data = docSnap.data();
        const { count } = data;

        if (count && typeof count === "object") {
            return count;
        } else {
            console.error("The data is not a map format.");
            return null;
        }
    } else {
        console.log("The document does not exist.");
        return null;
    }
}

export function updateHistogramBars(count) {
    const histogramContainer = document.querySelector(".histogram");
    if (!histogramContainer) {
        console.error("Histogram container bulunamadı.");
        return;
    }

    const totalVotes = Object.values(count).reduce(
        (sum, value) => sum + value,
        0
    );

    const bars = document.querySelectorAll(".bar");
    bars.forEach((bar) => {
        const value = bar.dataset.value;
        const voteCount = count[value] || 0;
        const height = totalVotes > 0 ? (voteCount / totalVotes) * 100 : 0;

        bar.style.height = `${height}%`;
        bar.style.width = "20%";
        bar.title = `Votes: ${voteCount}`;
    });
}

export function calculateAverage(count) {
    const totalVotes = Object.values(count).reduce(
        (sum, value) => sum + value,
        0
    );
    const weightedSum = Object.entries(count).reduce(
        (sum, [key, value]) => sum + (parseInt(key) * value),
        0
    );
    const average = totalVotes > 0 ? (weightedSum / totalVotes).toFixed(1) : 0;
    document.getElementById("average-score").innerText = `⭐️ ${average}`;
    // total value
    document.getElementById("total").innerText = `(${totalVotes})`;
}