/**
 * Infinite Scroll Posts Service
 * Handles infinite scrolling for forum posts with modern best practices
 */

// const API_URL = 'https://api-epv42bk6oq-uc.a.run.app';
const API_URL = 'http://localhost:4000';

/**
 * InfiniteScrollPosts class - Reusable infinite scroll implementation
 */
export class InfiniteScrollPosts {
    constructor(options = {}) {
        // Configuration
        this.apiEndpoint = options.apiEndpoint || `${API_URL}/api/posts`;
        this.limit = options.limit || 15;
        this.container = options.container;
        this.loadingIndicator = options.loadingIndicator;
        this.onPostsLoaded = options.onPostsLoaded || (() => {});
        this.onError = options.onError || ((error) => console.error('Error loading posts:', error));
        this.rootMargin = options.rootMargin || '100px';
        
        // State
        this.currentPage = 1;
        this.isLoading = false;
        this.hasMore = true;
        this.posts = [];
        
        // DOM elements
        this.sentinelElement = null;
        this.observer = null;
        
        // Bind methods
        this.handleIntersection = this.handleIntersection.bind(this);
        this.loadPosts = this.loadPosts.bind(this);
    }

    /**
     * Initialize the infinite scroll
     */
    async init() {
        if (!this.container) {
            throw new Error('Container element is required');
        }

        // Create and setup sentinel element for intersection observer
        this.createSentinel();
        
        // Setup intersection observer
        this.setupIntersectionObserver();
        
        // Load first page
        await this.loadFirstPage();
    }

    /**
     * Create sentinel element that triggers loading when visible
     */
    createSentinel() {
        this.sentinelElement = document.createElement('div');
        this.sentinelElement.className = 'scroll-sentinel';
        this.sentinelElement.style.height = '1px';
        this.sentinelElement.style.visibility = 'hidden';
        this.container.appendChild(this.sentinelElement);
    }

    /**
     * Setup IntersectionObserver for efficient scroll detection
     */
    setupIntersectionObserver() {
        const options = {
            root: null, // Use viewport as root
            rootMargin: this.rootMargin,
            threshold: 0.1
        };

        this.observer = new IntersectionObserver(this.handleIntersection, options);
        this.observer.observe(this.sentinelElement);
    }

    /**
     * Handle intersection observer callback
     */
    handleIntersection(entries) {
        const [entry] = entries;
        
        if (entry.isIntersecting && !this.isLoading && this.hasMore) {
            this.loadNextPage();
        }
    }

    /**
     * Load the first page of posts
     */
    async loadFirstPage() {
        this.currentPage = 1;
        this.posts = [];
        this.hasMore = true;
        
        await this.loadPosts(true);
    }

    /**
     * Load the next page of posts
     */
    async loadNextPage() {
        if (this.isLoading || !this.hasMore) {
            return;
        }

        this.currentPage++;
        await this.loadPosts(false);
    }

    /**
     * Core method to fetch posts from API
     */
    async loadPosts(isFirstPage = false) {
        if (this.isLoading) {
            return;
        }

        try {
            this.isLoading = true;
            this.showLoading();

            const url = `${this.apiEndpoint}?page=${this.currentPage}&limit=${this.limit}`;
            console.log(`Loading posts: ${url}`);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            // Validate response format
            if (!data.posts || !Array.isArray(data.posts)) {
                throw new Error('Invalid response format: posts array not found');
            }

            // Update state
            if (isFirstPage) {
                this.posts = data.posts;
            } else {
                this.posts = [...this.posts, ...data.posts];
            }

            this.hasMore = data.hasMore !== false; // Default to true if not specified

            // Call callback with new posts
            await this.onPostsLoaded(data.posts, isFirstPage, {
                currentPage: this.currentPage,
                hasMore: this.hasMore,
                totalPosts: this.posts.length
            });

            console.log(`Loaded ${data.posts.length} posts. Total: ${this.posts.length}, Has more: ${this.hasMore}`);

        } catch (error) {
            console.error('Error loading posts:', error);
            this.onError(error);
            
            // Revert page number on error
            if (!isFirstPage) {
                this.currentPage--;
            }
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    /**
     * Show loading indicator
     */
    showLoading() {
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = 'block';
        }
    }

    /**
     * Hide loading indicator
     */
    hideLoading() {
        if (this.loadingIndicator) {
            this.loadingIndicator.style.display = 'none';
        }
    }

    /**
     * Refresh posts (reload from first page)
     */
    async refresh() {
        await this.loadFirstPage();
    }

    /**
     * Get current state
     */
    getState() {
        return {
            currentPage: this.currentPage,
            isLoading: this.isLoading,
            hasMore: this.hasMore,
            totalPosts: this.posts.length,
            posts: [...this.posts] // Return copy to prevent external mutations
        };
    }

    /**
     * Destroy the infinite scroll instance
     */
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }

        if (this.sentinelElement && this.sentinelElement.parentNode) {
            this.sentinelElement.parentNode.removeChild(this.sentinelElement);
            this.sentinelElement = null;
        }

        // Reset state
        this.currentPage = 1;
        this.isLoading = false;
        this.hasMore = true;
        this.posts = [];
    }

    /**
     * Check if IntersectionObserver is supported
     */
    static isSupported() {
        return 'IntersectionObserver' in window;
    }
}

/**
 * Fallback implementation using scroll events for older browsers
 */
export class FallbackScrollPosts extends InfiniteScrollPosts {
    constructor(options = {}) {
        super(options);
        this.throttleDelay = options.throttleDelay || 100;
        this.threshold = options.threshold || 200; // pixels from bottom
        this.handleScroll = this.throttle(this.handleScroll.bind(this), this.throttleDelay);
    }

    setupIntersectionObserver() {
        // Use scroll events instead of IntersectionObserver
        window.addEventListener('scroll', this.handleScroll, { passive: true });
    }

    handleScroll() {
        if (this.isLoading || !this.hasMore) {
            return;
        }

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;

        if (scrollTop + windowHeight >= documentHeight - this.threshold) {
            this.loadNextPage();
        }
    }

    destroy() {
        window.removeEventListener('scroll', this.handleScroll);
        super.destroy();
    }

    // Simple throttle implementation
    throttle(func, delay) {
        let timeoutId;
        let lastExecTime = 0;
        
        return function (...args) {
            const currentTime = Date.now();
            
            if (currentTime - lastExecTime > delay) {
                func.apply(this, args);
                lastExecTime = currentTime;
            } else {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                    lastExecTime = Date.now();
                }, delay - (currentTime - lastExecTime));
            }
        };
    }
}

/**
 * Factory function to create appropriate infinite scroll instance
 */
export function createInfiniteScroll(options = {}) {
    if (InfiniteScrollPosts.isSupported()) {
        return new InfiniteScrollPosts(options);
    } else {
        console.warn('IntersectionObserver not supported, using fallback scroll implementation');
        return new FallbackScrollPosts(options);
    }
}
