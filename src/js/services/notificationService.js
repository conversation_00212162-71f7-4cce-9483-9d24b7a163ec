import { auth, db } from "/js/core/firebase_config.js";
import {
    collection,
    query,
    where,
    orderBy,
    limit,
    onSnapshot,
    doc,
    updateDoc,
    writeBatch,
    getDocs,
    getDoc,
    Timestamp
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

// API URL for backend requests
const API_URL = "https://api-epv42bk6oq-uc.a.run.app";

// Store the current notification listener to be able to detach it later
let notificationListener = null;

// Store the current notifications state
let currentNotifications = [];
let unreadCount = 0;

// Cache for usernames to avoid repeated fetches
const usernameCache = {};

/**
 * Initialize the notification system
 * @param {Function} onNotificationsUpdate - Callback function to be called when notifications are updated
 */
export function initNotifications(onNotificationsUpdate) {
    // Listen for auth state changes
    auth.onAuthStateChanged(user => {
        if (user) {
            // User is signed in, start listening for notifications
            startNotificationListener(onNotificationsUpdate);
        } else {
            // User is signed out, stop listening for notifications
            stopNotificationListener();

            // Reset notifications state
            currentNotifications = [];
            unreadCount = 0;

            // Call the callback with empty notifications
            if (onNotificationsUpdate) {
                onNotificationsUpdate([], 0);
            }
        }
    });
}

/**
 * Start listening for notifications
 * @param {Function} onNotificationsUpdate - Callback function to be called when notifications are updated
 */
function startNotificationListener(onNotificationsUpdate) {
    // Stop any existing listener
    stopNotificationListener();

    const user = auth.currentUser;
    if (!user) return;

    try {
        // Create a query for the user's notifications
        const notificationsRef = collection(db, "notifications");

        // Query for notifications with toUserId field
        const notificationsQuery = query(
            notificationsRef,
            where("toUserId", "==", user.uid),
            orderBy("createdAt", "desc"),
            limit(20)
        );

        // Start listening for notifications
        notificationListener = onSnapshot(notificationsQuery, async (snapshot) => {
            const notifications = [];
            let newUnreadCount = 0;
            const usernamePromises = [];

            snapshot.forEach((doc) => {
                const data = doc.data();
                const notification = {
                    id: doc.id,
                    ...data,
                    createdAt: data.createdAt?.toDate() || new Date(),
                    // Normalize fields
                    recipientId: data.toUserId,
                    senderId: data.fromUserId,
                    postId: data.questionId,
                    // Use fromUsername if available
                    senderUsername: data.fromUsername || data.senderUsername || ''
                };

                // If we don't have a username but have a user ID, fetch the username
                if (!notification.senderUsername && notification.senderId) {
                    // Add a promise to fetch the username
                    usernamePromises.push(
                        fetchUsername(notification.senderId).then(username => {
                            notification.senderUsername = username;
                        })
                    );
                }

                notifications.push(notification);

                // Count unread notifications
                if (!notification.isRead) {
                    newUnreadCount++;
                }
            });

            // Wait for all username fetches to complete
            if (usernamePromises.length > 0) {
                await Promise.all(usernamePromises);
            }

            // Sort notifications by date (newest first)
            notifications.sort((a, b) => b.createdAt - a.createdAt);

            // Update the current notifications state
            currentNotifications = notifications;
            unreadCount = newUnreadCount;

            // Call the callback with the updated notifications
            if (onNotificationsUpdate) {
                onNotificationsUpdate(notifications, newUnreadCount);
            }
        }, (error) => {
            console.error("Error listening for notifications:", error);
        });
    } catch (error) {
        console.error("Error setting up notification listener:", error);
    }
}

/**
 * Stop listening for notifications
 */
function stopNotificationListener() {
    if (notificationListener) {
        notificationListener();
        notificationListener = null;
    }
}

/**
 * Mark a notification as read
 * @param {string} notificationId - The ID of the notification to mark as read
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function markNotificationAsRead(notificationId) {
    try {
        const user = auth.currentUser;
        if (!user) {
            console.error("User not authenticated");
            return false;
        }

        // Call the backend API to mark the notification as read
        const response = await fetch(`${API_URL}/api/notifications/${notificationId}/read`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${await user.getIdToken()}`
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to mark notification as read: ${response.status}`);
        }

        // Also update in Firestore directly for immediate UI update
        const notificationRef = doc(db, "notifications", notificationId);
        await updateDoc(notificationRef, {
            isRead: true
        });

        return true;
    } catch (error) {
        console.error("Error marking notification as read:", error);
        return false;
    }
}

/**
 * Mark all notifications as read
 * @returns {Promise<boolean>} - Whether the operation was successful
 */
export async function markAllNotificationsAsRead() {
    try {
        const user = auth.currentUser;
        if (!user) {
            console.error("User not authenticated");
            return false;
        }

        // Call the backend API to mark all notifications as read
        const response = await fetch(`${API_URL}/api/notifications/read-all`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${await user.getIdToken()}`
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to mark all notifications as read: ${response.status}`);
        }

        // Also update in Firestore directly for immediate UI update
        const batch = writeBatch(db);
        const notificationsRef = collection(db, "notifications");

        // Update notifications
        const notificationsQuery = query(
            notificationsRef,
            where("toUserId", "==", user.uid),
            where("isRead", "==", false)
        );

        const querySnapshot = await getDocs(notificationsQuery);
        querySnapshot.forEach((doc) => {
            batch.update(doc.ref, { isRead: true });
        });

        await batch.commit();

        return true;
    } catch (error) {
        console.error("Error marking all notifications as read:", error);
        return false;
    }
}

/**
 * Get the current notifications
 * @returns {Array} - The current notifications
 */
export function getCurrentNotifications() {
    return currentNotifications;
}

/**
 * Get the current unread count
 * @returns {number} - The current unread count
 */
export function getUnreadCount() {
    return unreadCount;
}

/**
 * Format a notification message based on its type
 * @param {Object} notification - The notification object
 * @returns {string} - The formatted message
 */
export function formatNotificationMessage(notification) {
    // Get the username from notification data
    const username = notification.senderUsername || notification.fromUsername || 'User';

    // Always use the username in the message
    switch (notification.type) {
        case 'reply':
            return `${username} replied to your post`;
        case 'mention':
            return `${username} mentioned you in a post`;
        case 'follow_post':
            return `${username} published a new post`;
        default:
            return notification.message || 'You have a new notification';
    }
}

/**
 * Fetch a username for a user ID
 * @param {string} userId - The user ID to fetch the username for
 * @returns {Promise<string>} - The username or empty string if not found
 */
async function fetchUsername(userId) {
    if (!userId) return '';

    // Check cache first
    if (usernameCache[userId]) {
        return usernameCache[userId];
    }

    try {
        // Try to get the user document from Firestore
        const userRef = doc(db, "users", userId);
        const userDoc = await getDoc(userRef);

        if (userDoc.exists()) {
            const userData = userDoc.data();
            const username = userData.username || userData.displayName || '';

            // Cache the result
            if (username) {
                usernameCache[userId] = username;
            }

            return username;
        }

        return '';
    } catch (error) {
        console.error("Error fetching username:", error);
        return '';
    }
}

/**
 * Format the notification time
 * @param {Date} date - The date to format
 * @returns {string} - The formatted time
 */
export function formatNotificationTime(date) {
    if (!date) return '';

    const now = new Date();
    const diff = now - date;

    // Less than a minute
    if (diff < 60 * 1000) {
        return 'Just now';
    }

    // Less than an hour
    if (diff < 60 * 60 * 1000) {
        const minutes = Math.floor(diff / (60 * 1000));
        return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    }

    // Less than a day
    if (diff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(diff / (60 * 60 * 1000));
        return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    }

    // Less than a week
    if (diff < 7 * 24 * 60 * 60 * 1000) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1000));
        return `${days} day${days !== 1 ? 's' : ''} ago`;
    }

    // Format as date
    return date.toLocaleDateString();
}
