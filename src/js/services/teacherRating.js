import { app, db } from './core/firebase_config.js';
import { collection, addDoc } from "https://www.gstatic.com/firebasejs/9.21.0/firebase-firestore.js";

// Get the form element
const teacherRatingForm = document.getElementById('teacherRatingForm');
const descriptionTextarea = document.getElementById('description');
const wordCountDisplay = document.createElement('div');
wordCountDisplay.className = 'word-count';
wordCountDisplay.style.fontSize = '0.8rem';
wordCountDisplay.style.color = '#666';
wordCountDisplay.style.marginTop = '4px';
descriptionTextarea.parentNode.insertBefore(wordCountDisplay, descriptionTextarea.nextSibling);

// Function to count words
function countWords(text) {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

// Update word count display
function updateWordCount() {
    const wordCount = countWords(descriptionTextarea.value);
    const remaining = 1000 - wordCount;
    wordCountDisplay.textContent = `${wordCount}/1000 words`;
    wordCountDisplay.style.color = remaining < 0 ? '#d32f2f' : '#666';
}

// Add input event listener for real-time word counting
descriptionTextarea.addEventListener('input', updateWordCount);

// Handle form submission
teacherRatingForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    // Get form values
    const lecturer = document.getElementById('teacherName').value;
    const courseCode = document.getElementById('courseCode').value;
    const rating = document.getElementById('rating').value;
    const grade = document.getElementById('grade').value;
    const description = document.getElementById('description').value;

    // Check word count
    const wordCount = countWords(description);
    if (wordCount > 1000) {
        alert('Please limit your experience description to 1000 words.');
        return;
    }

    try {
        // Add the rating to Firestore
        const docRef = await addDoc(collection(db, 'teacherRatings'), {
            lecturer,
            courseCode,
            rating: parseInt(rating),
            grade,
            description
        });

        alert('Rating submitted successfully!');
        teacherRatingForm.reset();
        updateWordCount(); // Reset word counter

        // Redirect to the main hoca-database page
        window.location.href = 'index.html';
    } catch (error) {
        console.error('Error submitting rating:', error);
        alert('Error submitting rating. Please try again.');
    }
});

// Initialize word count display
updateWordCount();