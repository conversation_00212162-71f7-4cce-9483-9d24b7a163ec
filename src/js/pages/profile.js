import { auth } from "/js/core/firebase_config.js";
import {
  getIdToken,
  onAuthStateChanged,
  signOut
} from "https://www.gstatic.com/firebasejs/9.21.0/firebase-auth.js";
import { renderPosts } from "/js/components/post.js";
import { contentOverlay } from "/js/utils/contentOverlay.js";

// API URL for backend requests
// Always use the production API
const API_URL = "https://api-epv42bk6oq-uc.a.run.app";

// DOM elements
let loadingElement;
let profileContentElement;
let errorMessageElement;
let profileUsernameElement;
let userQuestionsElement;
let noQuestionsElement;
let logoutContainer;
let logoutButton;

// Pagination state
let lastVisibleId = null;
let hasMoreQuestions = true;
let currentUserId = null;

// Initialize DOM elements
function initDomElements() {
  loadingElement = document.getElementById("loading");
  profileContentElement = document.getElementById("profile-content");
  errorMessageElement = document.getElementById("error-message");
  profileUsernameElement = document.getElementById("profile-username");
  userQuestionsElement = document.getElementById("user-questions");
  noQuestionsElement = document.getElementById("no-questions");
  logoutContainer = document.getElementById("logout-container");
  logoutButton = document.getElementById("logout-button");

  // Initialize logout button functionality
  if (logoutButton) {
    logoutButton.addEventListener("click", handleLogout);
  }
}

/**
 * Handle logout button click
 * Signs out the user and redirects to the home page
 */
async function handleLogout() {
  try {
    // Disable the button to prevent multiple clicks
    logoutButton.disabled = true;
    logoutButton.textContent = "Signing Out...";

    // Sign out from Firebase Auth
    await signOut(auth);

    // Redirect to home page after successful sign out
    window.location.href = "/index.html";
  } catch (error) {
    console.error("Logout error:", error);

    // Re-enable the button
    logoutButton.disabled = false;
    logoutButton.textContent = "Sign Out";

    // Show error message
    alert("Failed to sign out. Please try again.");
  }
}

/**
 * Fetch user profile data from the API
 * @param {string} userId - The ID of the user to fetch
 * @returns {Promise<Object>} - The user profile data
 */
async function fetchUserProfileFromLocalAPI(userId) {
  try {
    console.log(`Fetching user profile for userId: ${userId}`);

    try {
      // Use the production API
      const response = await fetch(
        `${API_URL}/api/users/profile/${userId}`
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch user profile: ${response.status}`);
      }

      const userData = await response.json();
      console.log("User profile data:", userData);
      return userData;
    } catch (fetchError) {
      console.error("Error fetching user profile:", fetchError);
      throw fetchError;
    }
  } catch (error) {
    console.error("Error in fetchUserProfileFromLocalAPI:", error);
    throw error;
  }
}

/**
 * Load a user profile by userId
 * @param {string} userId - The ID of the user to load
 */
async function loadUserProfile(userId) {
  try {
    // Fetch user data from the local API
    const userData = await fetchUserProfileFromLocalAPI(userId);

    if (!userData) {
      throw new Error("User not found");
    }

    // Make sure userData has the userId
    if (!userData.userId) {
      userData.userId = userId;
      console.log("Setting userData.userId from parameter:", userId);
    }

    // Use onAuthStateChanged to ensure we have the latest auth state
    // This is more reliable than checking auth.currentUser directly
    onAuthStateChanged(auth, (user) => {
      console.log("Auth state changed in loadUserProfile:", {
        user: user,
        uid: user ? user.uid : null,
        isAnonymous: user ? user.isAnonymous : null,
        emailVerified: user ? user.emailVerified : null
      });

      // Check if user is logged in
      const isLoggedIn = !!user;

      // Check if this is the current user's profile
      const isCurrentUserProfile = isLoggedIn && user.uid === userId;

      console.log("User profile check after auth state change:", {
        profileUserId: userId,
        currentUserId: isLoggedIn ? user.uid : "not logged in",
        isLoggedIn: isLoggedIn,
        isCurrentUserProfile: isCurrentUserProfile
      });

      // Display user profile with the updated auth state
      displayUserProfile(userData, isLoggedIn, isCurrentUserProfile);
    });

    // Initial display with current auth state (may be updated later)
    // Log auth state in loadUserProfile
    console.log("Initial auth state in loadUserProfile:", {
      authCurrentUser: auth.currentUser,
      authCurrentUserUid: auth.currentUser ? auth.currentUser.uid : null,
      authIsAnonymous: auth.currentUser ? auth.currentUser.isAnonymous : null,
      authEmailVerified: auth.currentUser ? auth.currentUser.emailVerified : null
    });

    // Check if user is logged in
    const isLoggedIn = !!auth.currentUser;

    // Check if this is the current user's profile
    const isCurrentUserProfile = isLoggedIn && auth.currentUser.uid === userId;

    console.log("Initial user profile check:", {
      profileUserId: userId,
      currentUserId: isLoggedIn ? auth.currentUser.uid : "not logged in",
      isLoggedIn: isLoggedIn,
      isCurrentUserProfile: isCurrentUserProfile
    });

    // Initial display with current auth state
    displayUserProfile(userData, isLoggedIn, isCurrentUserProfile);

    // Update section title to show we're viewing someone else's questions
    const sectionTitle = document.querySelector(".profile-section-title");
    if (sectionTitle) {
      if (isCurrentUserProfile) {
        sectionTitle.textContent = "My Questions";
      } else {
        sectionTitle.textContent = `Questions by ${
          userData.username || "User"
        }`;
      }
    }

    // Update the no-questions message
    if (noQuestionsElement) {
      if (isCurrentUserProfile) {
        noQuestionsElement.innerHTML =
          'You haven\'t asked any questions yet. <a href="/pages/forum/create-question.html">Ask your first question!</a>';
      } else {
        noQuestionsElement.innerHTML =
          "This user hasn't asked any questions yet.";
      }
    }

    // Show loading indicator for questions
    const loadingIndicator = document.createElement("div");
    loadingIndicator.id = "questions-loading";
    loadingIndicator.className = "loading";
    loadingIndicator.textContent = "Loading questions...";
    userQuestionsElement.innerHTML = "";
    userQuestionsElement.appendChild(loadingIndicator);

    try {
      // If the user has question IDs in their profile, fetch those questions
      if (userData.questions && userData.questions.length > 0) {
        console.log("Fetching questions for user from API:", userId);

        try {
          // First try to fetch questions from the API endpoint
          const userQuestions = await fetchQuestionsFromAPI(userId, 10, null);
          console.log("Fetched questions from API:", userQuestions);

          // Display user's questions
          displayUserQuestions(userQuestions);
        } catch (apiError) {
          console.error(
            "Error fetching from API, falling back to question IDs:",
            apiError
          );

          // If API fails, we can try to fetch questions by their IDs
          // This is a fallback approach using the question IDs from the user profile
          try {
            // Fetch questions directly from the questions collection using IDs
            // For now, just display what we have
            const questionIds = userData.questions.slice(0, 10); // Take first 10 for pagination
            console.log("Using question IDs from profile:", questionIds);

            // Here you would normally fetch the actual question data
            // For now, we'll create placeholder objects with the IDs
            const placeholderQuestions = questionIds.map((id) => ({
              id: id,
              title: `Question ${id.substring(0, 6)}...`,
              createdAt: userData.createdAt || new Date(),
              userId: userId,
              username: userData.username,
            }));

            displayUserQuestions(placeholderQuestions);
          } catch (fallbackError) {
            console.error("Fallback also failed:", fallbackError);
            displayUserQuestions([]);
          }
        }
      } else {
        // No questions found
        displayUserQuestions([]);
      }
    } catch (error) {
      console.error("Error fetching questions from API:", error);

      // Show a simple error message
      const errorDiv = document.createElement("div");
      errorDiv.className = "error-message";
      errorDiv.textContent = `Error loading questions. Please try refreshing the page.`;
      userQuestionsElement.innerHTML = "";
      userQuestionsElement.appendChild(errorDiv);
    }

    // Show profile content
    loadingElement.style.display = "none";
    profileContentElement.style.display = "block";
  } catch (error) {
    console.error("Error loading user profile:", error);

    // Check if user is authenticated
    const isAuthenticated = !!auth.currentUser;

    // Show error message but don't show auth links if user is already authenticated
    showError(
      "Failed to load user profile. The user may not exist or there was a server error.",
      !isAuthenticated
    );
  }
}

function initProfilePage() {
  // Initialize DOM elements first
  initDomElements();

  // Show loading indicator
  loadingElement.style.display = "block";
  profileContentElement.style.display = "none";
  errorMessageElement.style.display = "none";

  // Log initial auth state
  console.log("Initial auth state in initProfilePage:", {
    authCurrentUser: auth.currentUser,
    authCurrentUserUid: auth.currentUser ? auth.currentUser.uid : null,
    authIsAnonymous: auth.currentUser ? auth.currentUser.isAnonymous : null,
    authEmailVerified: auth.currentUser ? auth.currentUser.emailVerified : null
  });

  // Check if a userId is provided in the URL
  const urlParams = new URLSearchParams(window.location.search);
  const profileUserId = urlParams.get("userId");

  if (profileUserId) {
    // If a userId is provided, we're viewing someone else's profile
    console.log("Viewing profile for user ID:", profileUserId);

    // Check if this is actually the current user's profile
    if (auth.currentUser && auth.currentUser.uid === profileUserId) {
      console.log("This is actually the current user's profile");
    }

    loadUserProfile(profileUserId);
  } else {
    // Otherwise, show the current user's profile
    onAuthStateChanged(auth, (user) => {
      console.log(
        "Auth state changed:",
        user ? "User logged in" : "User not logged in"
      );

      // Log detailed auth state after change
      console.log("Detailed auth state after change:", {
        user: user,
        uid: user ? user.uid : null,
        isAnonymous: user ? user.isAnonymous : null,
        emailVerified: user ? user.emailVerified : null
      });

      if (user) {
        handleAuthenticatedUser(user);
      } else {
        // User is not signed in
        console.log("User is not logged in");
        loadingElement.style.display = "none";
        showError("Please login to your account to view your profile.");
      }
    });
  }
}

async function handleAuthenticatedUser(user) {
  try {
    // Get the current user's ID token
    let idToken;
    try {
      idToken = await getIdToken(user, true);
      console.log("ID token obtained:", idToken ? "Yes" : "No");

      if (!idToken) {
        throw new Error("No authentication token available");
      }
    } catch (tokenError) {
      console.error("Error getting authentication token:", tokenError);
      // Don't show auth links since we know the user is already authenticated
      showError("Error getting authentication token. Please try again.", false);
      return;
    }

    // Fetch user profile data from backend
    const userData = await fetchUserProfile(idToken);

    // Make sure userData has the current user's ID
    if (!userData.userId && user.uid) {
      userData.userId = user.uid;
    }

    // Display user profile (this is the current user's profile)
    // We know the user is logged in and this is their own profile
    displayUserProfile(userData, true, true);

    // Show loading indicator for questions
    const questionsSection = document.querySelector(".profile-section");
    if (questionsSection) {
      const loadingIndicator = document.createElement("div");
      loadingIndicator.id = "questions-loading";
      loadingIndicator.className = "loading";
      loadingIndicator.textContent = "Loading questions...";
      userQuestionsElement.innerHTML = "";
      userQuestionsElement.appendChild(loadingIndicator);
    }

    try {
      // Fetch questions directly from the API endpoint
      console.log("Fetching questions for user from API:", user.uid);
      const userQuestions = await fetchQuestionsFromAPI(user.uid, 10, null);
      console.log("Fetched questions from API:", userQuestions);

      // Display user's questions
      displayUserQuestions(userQuestions);
    } catch (error) {
      console.error("Error fetching questions from API:", error);

      // Show a simple error message
      const errorDiv = document.createElement("div");
      errorDiv.className = "error-message";
      errorDiv.textContent = `Error loading questions. Please try refreshing the page.`;
      userQuestionsElement.innerHTML = "";
      userQuestionsElement.appendChild(errorDiv);
    }

    // Show profile content
    loadingElement.style.display = "none";
    profileContentElement.style.display = "block";
  } catch (error) {
    console.error("Error loading profile:", error);
    // Don't show auth links since we know the user is already authenticated
    showError("Failed to load profile data. Please try again later.", false);
  }
}

async function fetchUserProfile(idToken) {
  try {
    if (!idToken) {
      throw new Error("Authentication token is required to fetch user profile");
    }

    try {
      // Use the production API
      const response = await fetch(`${API_URL}/api/users/profile`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${idToken}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const userData = await response.json();
        if (!userData) {
          throw new Error("User profile data is incomplete");
        }
        console.log("User profile data:", userData);
        return userData;
      } else {
        throw new Error(
          `Failed to fetch user profile. Status: ${response.status}`
        );
      }
    } catch (fetchError) {
      console.error("Error fetching user profile:", fetchError);
      throw fetchError;
    }
  } catch (error) {
    console.error("Error in fetchUserProfile function:", error);
    throw error;
  }
}

async function fetchQuestionsFromAPI(userId, limit = 10, lastVisible = null) {
  try {
    // Store the current user ID for pagination
    currentUserId = userId;

    // Construct the API URL
    let url = `${API_URL}/api/posts/${userId}?limit=${limit}`;
    if (lastVisible) {
      url += `&lastVisible=${lastVisible}`;
    }

    console.log("Fetching questions from URL:", url);

    try {
      // Use the production API
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        // Parse the response
        const data = await response.json();
        console.log("API response:", data);

        // Process the response data
        processApiResponse(data, limit);

        // Return the data in the appropriate format
        return extractQuestionsFromResponse(data);
      } else {
        const errorText = await response.text().catch(() => "No error text available");
        console.error(
          `Error fetching user questions. Status: ${response.status}`,
          errorText
        );
        throw new Error(`Failed to fetch questions. Status: ${response.status}`);
      }
    } catch (fetchError) {
      console.error("Error fetching questions:", fetchError);
      throw fetchError;
    }
  } catch (error) {
    console.error("Error in fetchQuestionsFromAPI:", error);
    throw error;
  }
}

/**
 * Process API response data to update pagination state
 * @param {Object} data - The API response data
 * @param {number} limit - The requested limit of items
 */
function processApiResponse(data, limit) {
  // Update pagination state
  if (data.lastVisible) {
    lastVisibleId = data.lastVisible;
  }

  // Check if there are more questions based on the response format
  let responseItems = [];
  if (data.posts) {
    responseItems = data.posts;
  } else if (data.questions) {
    responseItems = data.questions;
  } else if (Array.isArray(data)) {
    responseItems = data;
  }

  // If we got fewer items than the limit, or no items at all, there are no more questions
  hasMoreQuestions =
    responseItems.length > 0 && responseItems.length === limit;

  console.log("Updated pagination state:", {
    lastVisibleId,
    hasMoreQuestions,
  });
}

/**
 * Extract questions from the API response in the appropriate format
 * @param {Object} data - The API response data
 * @returns {Array} - The extracted questions
 */
function extractQuestionsFromResponse(data) {
  // Check different possible response formats
  if (data.posts) {
    return data.posts;
  } else if (data.questions) {
    return data.questions;
  } else if (Array.isArray(data)) {
    return data;
  } else {
    console.warn("Unexpected response format:", data);
    return [];
  }
}

function displayUserProfile(userData, isLoggedIn = false, isCurrentUserProfile = false) {
  // Display username
  const username = userData.username || "User";
  profileUsernameElement.textContent = username;

  // Show/hide logout button based on authentication state
  if (logoutContainer) {
    if (isLoggedIn && isCurrentUserProfile) {
      logoutContainer.style.display = "block";
    } else {
      logoutContainer.style.display = "none";
    }
  }

  // Create additional profile information
  const profileHeader = document.querySelector(".profile-header");

  // Remove any existing profile info
  const existingInfo = document.querySelector(".profile-info");
  if (existingInfo) {
    existingInfo.remove();
  }

  // Update profile actions
  const profileActions = document.querySelector(".profile-actions");
  if (profileActions) {
    // Clear existing buttons
    profileActions.innerHTML = "";

    // Get the userId from the URL or use the userData.userId
    const urlParams = new URLSearchParams(window.location.search);
    const urlUserId = urlParams.get("userId");

    // Make sure userData has a userId
    if (!userData.userId && urlUserId) {
      userData.userId = urlUserId;
      console.log("Setting userData.userId from URL:", urlUserId);
    }

    const profileUserId = urlUserId || userData.userId;

    console.log("Profile user ID determination:", {
      urlUserId: urlUserId,
      userDataUserId: userData.userId,
      finalProfileUserId: profileUserId
    });

    // Log auth state and parameters
    console.log("Auth state in displayUserProfile:", {
      isLoggedIn: isLoggedIn,
      isCurrentUserProfile: isCurrentUserProfile,
      authCurrentUser: auth.currentUser,
      authCurrentUserUid: auth.currentUser ? auth.currentUser.uid : null
    });

    // Determine if this is the current user's profile
    // Use the passed parameters instead of recalculating
    const isOwnProfile = isCurrentUserProfile;

    console.log("Profile comparison:", {
      profileUserId: profileUserId,
      userDataUserId: userData.userId,
      isLoggedIn: isLoggedIn,
      isOwnProfile: isOwnProfile
    });

    // Add Ask Question button ONLY if viewing own profile
    if (isOwnProfile) {
      console.log("Adding Ask Question button - this is the user's own profile");
      const askQuestionButton = document.createElement("a");
      askQuestionButton.href = "/pages/forum/create-question.html";
      askQuestionButton.className = "ask-question-button";
      askQuestionButton.textContent = "Ask Question";
      profileActions.appendChild(askQuestionButton);
    }

    // Add Follow button if NOT viewing own profile (either another user's profile or not logged in)
    if (!isOwnProfile) {
      console.log("Adding Follow button - this is NOT the user's own profile");
      const followButton = document.createElement("button");
      followButton.className = "follow-button";
      followButton.id = "follow-button";

      // Check if the current user is already following this profile
      const isFollowing =
        isLoggedIn &&
        auth.currentUser &&
        userData.followers &&
        Array.isArray(userData.followers) &&
        userData.followers.includes(auth.currentUser.uid);

      if (isFollowing) {
        followButton.textContent = "Unfollow";
        followButton.classList.add("following");
      } else {
        followButton.textContent = "Follow";
      }

      // Add click event listener
      followButton.addEventListener("click", () =>
        handleFollowButtonClick(profileUserId, isFollowing)
      );

      profileActions.appendChild(followButton);
    }
  }

  // Create profile info container
  const profileInfo = document.createElement("div");
  profileInfo.className = "profile-info";

  // Add join date if available
  if (userData.createdAt) {
    let joinDate;
    try {
      // Try to parse the date
      if (
        typeof userData.createdAt === "object" &&
        userData.createdAt !== null
      ) {
        // Handle Firestore timestamp
        if (userData.createdAt.seconds) {
          joinDate = new Date(userData.createdAt.seconds * 1000);
        } else if (userData.createdAt._seconds) {
          joinDate = new Date(userData.createdAt._seconds * 1000);
        }
      } else if (typeof userData.createdAt === "string") {
        joinDate = new Date(userData.createdAt);
      } else if (typeof userData.createdAt === "number") {
        joinDate = new Date(userData.createdAt);
      }

      if (joinDate && !isNaN(joinDate.getTime())) {
        const joinDateElement = document.createElement("p");
        joinDateElement.className = "join-date";
        joinDateElement.textContent = `Joined: ${joinDate.toLocaleDateString()}`;
        profileInfo.appendChild(joinDateElement);
      }
    } catch (error) {
      console.error("Error parsing join date:", error);
    }
  }

  // Add question count
  const questionCount = userData.questions ? userData.questions.length : 0;
  const questionCountElement = document.createElement("p");
  questionCountElement.className = "question-count";
  questionCountElement.textContent = `Questions: ${questionCount}`;
  profileInfo.appendChild(questionCountElement);

  // Add followers/following counts if available
  if (userData.followers || userData.following) {
    const socialStatsElement = document.createElement("div");
    socialStatsElement.className = "social-stats";

    if (userData.followers) {
      const followersCount = Array.isArray(userData.followers)
        ? userData.followers.length
        : 0;
      const followersElement = document.createElement("span");
      followersElement.className = "followers-count";
      followersElement.textContent = `Followers: ${followersCount}`;
      socialStatsElement.appendChild(followersElement);
    }

    if (userData.following) {
      const followingCount = Array.isArray(userData.following)
        ? userData.following.length
        : 0;
      const followingElement = document.createElement("span");
      followingElement.className = "following-count";
      followingElement.textContent = `Following: ${followingCount}`;

      if (socialStatsElement.firstChild) {
        // Add separator if we already have followers count
        const separator = document.createElement("span");
        separator.className = "stats-separator";
        separator.textContent = " • ";
        socialStatsElement.appendChild(separator);
      }

      socialStatsElement.appendChild(followingElement);
    }

    profileInfo.appendChild(socialStatsElement);
  }

  // Insert the profile info after the username
  if (profileHeader && profileInfo.children.length > 0) {
    profileHeader.insertBefore(
      profileInfo,
      profileHeader.querySelector(".profile-actions")
    );
  }

  console.log("User Profile Data:", userData);
}

function displayUserQuestions(questions, append = false) {
  const loadingIndicator = document.getElementById("questions-loading");
  if (loadingIndicator) {
    loadingIndicator.remove();
  }

  // Remove existing load more button if present
  const existingLoadMoreButton = document.getElementById("load-more-button");
  if (existingLoadMoreButton) {
    existingLoadMoreButton.remove();
  }

  if (!append) {
    userQuestionsElement.innerHTML = "";
  }

  // Update questions count
  const questionsCountElement = document.getElementById("questions-count");
  if (questionsCountElement && !append) {
    if (questions && questions.length > 0) {
      questionsCountElement.textContent = `Showing ${
        questions.length
      } question${questions.length > 1 ? "s" : ""}`;
      questionsCountElement.style.display = "block";
    } else {
      questionsCountElement.style.display = "none";
    }
  }

  if (!questions || questions.length === 0) {
    console.log("No questions to display");

    if (!append) {
      // Create a simple message
      const noQuestionsMessage = document.createElement("div");
      noQuestionsMessage.className = "no-questions-message";
      noQuestionsMessage.textContent =
        "No questions found. Try asking a question in the forum.";

      userQuestionsElement.appendChild(noQuestionsMessage);
      noQuestionsElement.style.display = "block";
    }
    return;
  } else {
    console.log(`Displaying ${questions.length} questions`);
    noQuestionsElement.style.display = "none";
  }

  // Create a container for the posts if appending
  let postsContainer;
  if (append) {
    postsContainer = document.querySelector(".posts-container");
    if (!postsContainer) {
      postsContainer = document.createElement("div");
      postsContainer.className = "posts-container";
      userQuestionsElement.appendChild(postsContainer);
    }
  } else {
    postsContainer = document.createElement("div");
    postsContainer.className = "posts-container";
    userQuestionsElement.appendChild(postsContainer);
  }

  // Render the posts
  renderPosts(questions, postsContainer, "/pages/forum/question.html");

  // Initialize content overlay for anonymous users
  contentOverlay.init('.posts-container');

  // Add appropriate button based on pagination state
  if (hasMoreQuestions) {
    // Add "Load More" button if there are more questions
    const loadMoreButton = document.createElement("button");
    loadMoreButton.id = "load-more-button";
    loadMoreButton.className = "load-more-button";
    loadMoreButton.textContent = "Load More Questions";
    loadMoreButton.addEventListener("click", loadMoreQuestions);
    userQuestionsElement.appendChild(loadMoreButton);
  } else if (append) {
    // Add "Go Back to Top" button if we've loaded all questions and we're appending
    const goBackButton = document.createElement("button");
    goBackButton.id = "go-back-button";
    goBackButton.className = "go-back-button";
    goBackButton.textContent = "No More Questions - Go Back to Top";
    goBackButton.addEventListener("click", () => {
      // Scroll to the top of the page
      window.scrollTo({ top: 0, behavior: "smooth" });
    });
    userQuestionsElement.appendChild(goBackButton);
  }
}

async function loadMoreQuestions() {
  try {
    // Show loading indicator
    const loadMoreButton = document.getElementById("load-more-button");
    if (loadMoreButton) {
      loadMoreButton.textContent = "Loading...";
      loadMoreButton.disabled = true;
    }

    console.log("Loading more questions with lastVisibleId:", lastVisibleId);

    try {
      // Fetch more questions from the API
      const moreQuestions = await fetchQuestionsFromAPI(
        currentUserId,
        10,
        lastVisibleId
      );

      if (moreQuestions && moreQuestions.length > 0) {
        // Display the additional questions
        displayUserQuestions(moreQuestions, true);

        // Update the questions count
        const questionsCountElement =
          document.getElementById("questions-count");
        if (questionsCountElement) {
          const currentCount = document.querySelectorAll(".post").length;
          questionsCountElement.textContent = `Showing ${currentCount} question${
            currentCount > 1 ? "s" : ""
          }`;
        }
      } else {
        // No more questions to display
        hasMoreQuestions = false;

        // Remove the load more button
        const loadMoreButton = document.getElementById("load-more-button");
        if (loadMoreButton) {
          loadMoreButton.remove();
        }

        // Add a simple message
        const noMoreQuestionsMessage = document.createElement("div");
        noMoreQuestionsMessage.className = "no-more-questions-message";
        noMoreQuestionsMessage.textContent = "No more questions to load.";
        userQuestionsElement.appendChild(noMoreQuestionsMessage);

        // Add "Go Back to Top" button
        const goBackButton = document.createElement("button");
        goBackButton.id = "go-back-button";
        goBackButton.className = "go-back-button";
        goBackButton.textContent = "Go Back to Top";
        goBackButton.addEventListener("click", () => {
          // Scroll to the top of the page
          window.scrollTo({ top: 0, behavior: "smooth" });
        });
        userQuestionsElement.appendChild(goBackButton);
      }
    } catch (apiError) {
      console.error("Error fetching more questions:", apiError);

      // Show error message on the button
      if (loadMoreButton) {
        loadMoreButton.textContent = `Error: ${apiError.message}. Try again.`;
        loadMoreButton.disabled = false;
      }
    }
  } catch (error) {
    console.error("Error loading more questions:", error);

    // Show error message on the button
    const loadMoreButton = document.getElementById("load-more-button");
    if (loadMoreButton) {
      loadMoreButton.textContent = `Error: ${error.message}. Try again.`;
      loadMoreButton.disabled = false;
    }

    // Add a simple error message below the button
    const errorMessage = document.createElement("div");
    errorMessage.className = "error-message";
    errorMessage.textContent = `Error loading more questions. Please try again.`;
    userQuestionsElement.appendChild(errorMessage);
  }
}

/**
 * Show an error message to the user
 * @param {string} message - The error message to display
 * @param {boolean} showAuthLinks - Whether to show authentication links
 */
function showError(message, showAuthLinks = true) {
  loadingElement.style.display = "none";
  profileContentElement.style.display = "none";

  // Check if the user is already authenticated
  const isAuthenticated = !!auth.currentUser;

  // Only show auth links if requested and user is not already authenticated
  const authLinksHtml = (showAuthLinks && !isAuthenticated) ? `
    <div class="auth-links">
      <a href="/pages/auth/login.html" class="auth-link">Login to your account</a>
      <a href="/pages/auth/signup.html" class="auth-link">Create an account</a>
    </div>
  ` : '';

  // Update the error message content
  errorMessageElement.innerHTML = `
    <p>${message}</p>
    ${authLinksHtml}
  `;

  errorMessageElement.style.display = "block";
}

/**
 * Handle follow/unfollow button click
 * @param {string} targetUserId - The ID of the user to follow/unfollow
 * @param {boolean} isCurrentlyFollowing - Whether the current user is already following the target user
 */
async function handleFollowButtonClick(targetUserId, isCurrentlyFollowing) {
  try {
    // Log auth state in handleFollowButtonClick
    console.log("Auth state in handleFollowButtonClick:", {
      authCurrentUser: auth.currentUser,
      authCurrentUserUid: auth.currentUser ? auth.currentUser.uid : null,
      targetUserId: targetUserId,
      isCurrentlyFollowing: isCurrentlyFollowing
    });

    // Check if user is logged in
    if (!auth.currentUser) {
      console.log("User not logged in, redirecting to login page");
      alert("You need to be logged in to follow users");
      window.location.href = "/pages/auth/login.html";
      return;
    }

    const followButton = document.getElementById("follow-button");
    if (!followButton) return;

    // Disable button and show loading state
    const originalText = followButton.textContent;
    followButton.disabled = true;
    followButton.textContent = isCurrentlyFollowing
      ? "Unfollowing..."
      : "Following...";

    try {
      // Make API request to follow/unfollow user
      const action = isCurrentlyFollowing ? "unfollow" : "follow";
      const response = await fetch(
        `${API_URL}/api/users/${action}/${targetUserId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await auth.currentUser.getIdToken()}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to ${action} user: ${response.status}`);
      }

      // Update button state
      if (isCurrentlyFollowing) {
        followButton.textContent = "Follow";
        followButton.classList.remove("following");
      } else {
        followButton.textContent = "Unfollow";
        followButton.classList.add("following");
      }

      // Update follower count in the UI
      const followersCountElement = document.querySelector(".followers-count");
      if (followersCountElement) {
        const currentCount = parseInt(
          followersCountElement.textContent.match(/\d+/)[0]
        );
        const newCount = isCurrentlyFollowing
          ? currentCount - 1
          : currentCount + 1;
        followersCountElement.textContent = `Followers: ${newCount}`;
      }
    } catch (error) {
      console.error(
        `Error ${isCurrentlyFollowing ? "unfollowing" : "following"} user:`,
        error
      );

      // Restore button state
      followButton.textContent = originalText;
      if (isCurrentlyFollowing) {
        followButton.classList.add("following");
      }

      // Show error message
      alert(
        `Failed to ${
          isCurrentlyFollowing ? "unfollow" : "follow"
        } user. Please try again later.`
      );
    }

    // Re-enable button
    followButton.disabled = false;
  } catch (error) {
    console.error("Error handling follow button click:", error);
    alert("An error occurred. Please try again later.");
  }
}

// Initialize the profile page when the DOM is loaded
document.addEventListener("DOMContentLoaded", initProfilePage);
