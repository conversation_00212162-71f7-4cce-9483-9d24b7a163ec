import { db, auth } from '/js/core/firebase_config.js';
import { doc, getDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
import { onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
import { contentOverlay } from '/js/utils/contentOverlay.js';

/**
 * Get the question ID from the URL query parameters
 * @returns {string|null} The question ID or null if not found
 */
function getQuestionIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('id');
}

/**
 * Fetch question data from Firestore or API
 * @param {string} questionId - The ID of the question to fetch
 * @returns {Promise<Object|null>} The question data or null if not found
 */
async function fetchQuestionData(questionId) {
    try {
        // First try to fetch from Firestore
        const questionRef = doc(db, 'questions', questionId);
        const questionSnap = await getDoc(questionRef);

        if (questionSnap.exists()) {
            const questionData = questionSnap.data();
            questionData.id = questionSnap.id; // Add the document ID to the data
            return questionData;
        }

        // If not found in Firestore, try the API
        console.log('Question not found in Firestore, trying API...');
        try {
            const apiUrl = 'https://api-epv42bk6oq-uc.a.run.app';
            const response = await fetch(`${apiUrl}/api/posts/${questionId}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            // Check if the API returned a valid question
            if (data && (data.title || (data.post && data.post.title))) {
                // Handle different response structures
                const questionData = data.post || data;
                questionData.id = questionId; // Ensure ID is set
                return questionData;
            }
        } catch (apiError) {
            console.error('Error fetching from API:', apiError);
        }

        console.log('No such question found in Firestore or API');
        return null;
    } catch (error) {
        console.error('Error fetching question:', error);
        return null;
    }
}

/**
 * Display the question data on the page
 * @param {Object} questionData - The question data to display
 */
function displayQuestion(questionData) {
    // Hide loading indicator
    document.getElementById('loading').classList.add('hidden');

    // Show question content
    const questionContent = document.getElementById('question-content');
    questionContent.classList.remove('hidden');

    // Set question title
    document.getElementById('question-title').textContent = questionData.title || 'Untitled Question';

    // Set question date
    const questionDate = document.getElementById('question-date');
    if (questionData.createdAt) {
        let date;
        if (typeof questionData.createdAt === 'object' && questionData.createdAt.seconds) {
            // Firestore timestamp
            date = new Date(questionData.createdAt.seconds * 1000);
        } else {
            // Regular date string or timestamp
            date = new Date(questionData.createdAt);
        }
        questionDate.textContent = `Asked on ${date.toLocaleDateString()}`;
    } else {
        questionDate.textContent = 'Asked on unknown date';
    }

    // Set question body
    const bodyElement = document.getElementById('question-body');
    bodyElement.textContent = questionData.body || questionData.content || 'No content available';

    // Set question tags
    const tagsContainer = document.getElementById('question-tags');
    if (questionData.tags && Array.isArray(questionData.tags)) {
        questionData.tags.forEach(tag => {
            const tagElement = document.createElement('span');
            tagElement.className = 'tag';
            tagElement.textContent = tag;
            tagsContainer.appendChild(tagElement);
        });
    } else if (typeof questionData.tags === 'string') {
        // Handle tags as comma-separated string
        const tags = questionData.tags.split(',');
        tags.forEach(tag => {
            if (tag.trim()) {
                const tagElement = document.createElement('span');
                tagElement.className = 'tag';
                tagElement.textContent = tag.trim();
                tagsContainer.appendChild(tagElement);
            }
        });
    }

    // Set page title
    document.title = `${questionData.title || 'Question'} - MetuHub`;
}

/**
 * Show error message if question not found
 */
function showErrorMessage() {
    // Hide loading indicator
    document.getElementById('loading').classList.add('hidden');

    // Show error message
    document.getElementById('error-message').classList.remove('hidden');
}



/**
 * Fetch replies for a question from the backend API
 * @param {string} questionId - The ID of the question to fetch replies for
 * @returns {Promise<Array>} The replies data or empty array if none found
 */
async function fetchRepliesFromBackend(questionId) {
    try {
        // Use the production API endpoint directly
        const apiUrl = 'https://api-epv42bk6oq-uc.a.run.app';
        const url = `${apiUrl}/api/posts/${questionId}/replies`;

        console.log(`Fetching replies from: ${url}`);

        const response = await fetch(url);

        if (!response.ok) {
            const errorText = await response.text().catch(() => 'No error text available');
            console.error(`HTTP error fetching replies! Status: ${response.status}`, errorText);
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log(`Received ${Array.isArray(data) ? data.length : 0} replies from API`);

        if (!Array.isArray(data)) {
            console.warn('API did not return an array for replies, converting to empty array');
            return [];
        }

        return data;
    } catch (error) {
        console.error('Error fetching replies from API:', error);
        return [];
    }
}

/**
 * Fetch replies for a question from the API
 * @param {string} questionId - The ID of the question to fetch replies for
 * @returns {Promise<Array>} The replies data or empty array if none found
 */
async function fetchReplies(questionId) {
    try {
        // Only fetch from the backend API
        return await fetchRepliesFromBackend(questionId);
    } catch (error) {
        console.error('Error fetching replies from API:', error);
        return [];
    }
}

/**
 * Fetch username for a user ID
 * @param {string} userId - The user ID to fetch the username for
 * @returns {Promise<string>} The username or a default value if not found
 */
async function fetchUsername(userId) {
    try {
        // Use the production API endpoint directly
        const apiUrl = 'https://api-epv42bk6oq-uc.a.run.app';

        // First try to fetch from the API
        try {
            const response = await fetch(`${apiUrl}/api/users/${userId}`);

            if (response.ok) {
                const userData = await response.json();
                if (userData && userData.username) {
                    return userData.username;
                }
            }

            // Try alternative endpoint if the first one fails
            const altResponse = await fetch(`${apiUrl}/api/users/username/${userId}`);

            if (altResponse.ok) {
                const userData = await altResponse.json();
                if (userData && userData.username) {
                    return userData.username;
                }
            }
        } catch (apiError) {
            console.error(`API error fetching username for userId ${userId}:`, apiError);
        }

        // If API fails, try to fetch from Firestore directly
        try {
            const userRef = doc(db, 'users', userId);
            const userSnap = await getDoc(userRef);

            if (userSnap.exists()) {
                const userData = userSnap.data();
                if (userData && userData.username) {
                    return userData.username;
                }
            }
        } catch (firestoreError) {
            console.error(`Firestore error fetching username for userId ${userId}:`, firestoreError);
        }

        // Return a default value if all methods fail
        return `Anonymous User`;
    } catch (error) {
        console.error(`Error fetching username for userId ${userId}:`, error);
        return `Anonymous User`;
    }
}

/**
 * Format a date string or timestamp
 * @param {string|object} dateInput - The date to format
 * @returns {string} The formatted date string
 */
function formatDate(dateInput) {
    if (!dateInput) return 'unknown date';

    let date;
    try {
        if (typeof dateInput === 'object' && dateInput.seconds) {
            // Firestore timestamp
            date = new Date(dateInput.seconds * 1000);
        } else if (typeof dateInput === 'string') {
            // ISO date string
            date = new Date(dateInput);
        } else if (typeof dateInput === 'number') {
            // Unix timestamp
            date = new Date(dateInput);
        } else {
            date = new Date();
        }

        return date.toLocaleDateString();
    } catch (error) {
        console.error('Error formatting date:', error);
        return 'unknown date';
    }
}

/**
 * Create a reply element
 * @param {Object} reply - The reply data
 * @returns {HTMLElement} The reply element
 */
async function createReplyElement(reply) {
    const replyElement = document.createElement('div');
    replyElement.className = 'reply-item';
    replyElement.dataset.replyId = reply.id || '';

    // Fetch username if not provided
    let username = reply.username;
    if (!username && reply.userId) {
        username = await fetchUsername(reply.userId);
    }

    // Format the date
    const formattedDate = formatDate(reply.createdAt);

    // Get the content from the appropriate field
    const content = reply.content || reply.body || '';

    // Create a profile link if we have a userId
    const authorHtml = reply.userId
        ? `<a href="/pages/profile/profile.html?userId=${reply.userId}" class="username-link">${username || 'Anonymous User'}</a>`
        : `${username || 'Anonymous User'}`;

    replyElement.innerHTML = `
        <div class="reply-content">${content}</div>
        <div class="reply-footer">
            <span class="reply-author">${authorHtml}</span>
            <span class="reply-date">${formattedDate}</span>
        </div>
    `;

    return replyElement;
}

/**
 * Display replies for a question
 * @param {string} questionId - The ID of the question
 */
async function displayReplies(questionId) {
    const repliesContainer = document.getElementById('replies-container');
    let repliesLoading = document.getElementById('replies-loading');
    const noRepliesMessage = document.getElementById('no-replies-message');

    try {
        // Check if user is authenticated before showing loading states
        const user = auth.currentUser;
        const isAuthenticated = !!user;

        // Only show loading indicator for authenticated users
        if (isAuthenticated) {
            // Make sure we have a loading indicator
            if (!repliesLoading) {
                repliesLoading = document.createElement('div');
                repliesLoading.id = 'replies-loading';
                repliesLoading.className = 'replies-loading';
                repliesLoading.textContent = 'Loading replies...';
                repliesContainer.appendChild(repliesLoading);
            } else {
                repliesLoading.classList.remove('hidden');
            }
        }

        // Clear existing replies
        Array.from(repliesContainer.children).forEach(child => {
            if (child.id !== 'replies-loading' && child.id !== 'no-replies-message') {
                child.remove();
            }
        });

        // Only fetch replies for authenticated users
        let replies = [];
        if (isAuthenticated) {
            console.log('Fetching replies for question:', questionId);
            replies = await fetchReplies(questionId);
            console.log('Fetched replies:', replies);
        }

        // Hide loading indicator
        if (repliesLoading) {
            repliesLoading.classList.add('hidden');
        }

        if (!replies || replies.length === 0) {
            // Show no replies message
            noRepliesMessage.classList.remove('hidden');
            return;
        } else {
            // Hide no replies message
            noRepliesMessage.classList.add('hidden');
        }

        // Create and append reply elements
        const replyElements = await Promise.all(
            replies.map(reply => createReplyElement(reply))
        );

        // Append replies
        replyElements.forEach(element => {
            repliesContainer.appendChild(element);
        });

        // Update the reply count in the title
        const repliesCount = document.getElementById('replies-count');
        if (repliesCount) {
            repliesCount.textContent = `(${replies.length})`;
        }
    } catch (error) {
        console.error('Error displaying replies:', error);
        if (repliesLoading) {
            repliesLoading.textContent = 'Failed to load replies. Please try again later.';
            repliesLoading.classList.remove('hidden');
        }
    }
}



/**
 * Submit a new reply to the backend API
 * @param {string} questionId - The ID of the question
 * @param {string} content - The reply content
 * @param {string} userId - The user ID of the replier
 * @returns {Promise<boolean>} Whether the submission was successful
 */
async function submitReplyToBackend(questionId, content, userId) {
    try {
        // Use the production API endpoint directly
        const apiUrl = 'https://api-epv42bk6oq-uc.a.run.app';

        // Use the exact request body format as specified
        const requestBody = {
            content: content,
            userId: userId
        };

        console.log('Submitting reply with body:', requestBody);

        const response = await fetch(`${apiUrl}/api/posts/${questionId}/replies`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`API error (${response.status}):`, errorText);
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return true;
    } catch (error) {
        console.error('Error submitting reply to API:', error);
        return false;
    }
}

/**
 * Submit a new reply to the backend API
 * @param {string} questionId - The ID of the question
 * @param {string} content - The reply content
 * @param {string} userId - The user ID of the replier
 * @returns {Promise<boolean>} Whether the submission was successful
 */
async function submitReply(questionId, content, userId) {
    try {
        // Submit to the backend API
        const success = await submitReplyToBackend(questionId, content, userId);
        if (success) {
            console.log('Reply successfully submitted to API');
            return true;
        }

        return false;
    } catch (error) {
        console.error('Error submitting reply to API:', error);
        return false;
    }
}

/**
 * Initialize the reply form
 * @param {string} questionId - The ID of the question
 */
function initReplyForm(questionId) {
    const replyForm = document.getElementById('reply-form');
    const replyContent = document.getElementById('reply-content');
    const replyLoginMessage = document.getElementById('reply-login-message');

    // Flag to track if we've already added the event listener
    let listenerAdded = false;

    // Function to handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();

        // Get current user
        const user = auth.currentUser;
        if (!user) {
            console.error('User not logged in');
            return;
        }

        const content = replyContent.value.trim();
        if (!content) return;

        // Disable form while submitting
        const submitButton = replyForm.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.textContent = 'Posting...';

        // Submit the reply
        const success = await submitReply(questionId, content, user.uid);

        if (success) {
            // Clear the form
            replyContent.value = '';

            // Add a small delay before refreshing to allow the server to process the new reply
            console.log('Reply submitted successfully, refreshing replies in 500ms...');
            setTimeout(async () => {
                try {
                    // Fetch and display replies again
                    await displayReplies(questionId);
                    console.log('Replies refreshed successfully');
                } catch (refreshError) {
                    console.error('Error refreshing replies:', refreshError);
                    alert('Your reply was posted, but we had trouble refreshing the replies. Please refresh the page to see your reply.');
                }
            }, 500);
        } else {
            alert('Failed to post reply. Please try again.');
        }

        // Re-enable the form
        submitButton.disabled = false;
        submitButton.textContent = 'Post Reply';
    };

    // Check if user is logged in
    onAuthStateChanged(auth, (user) => {
        if (user) {
            // User is logged in, show the form
            replyForm.classList.remove('hidden');
            replyLoginMessage.classList.add('hidden');

            // Add event listener only once
            if (!listenerAdded) {
                // Remove any existing listeners first (just to be safe)
                replyForm.removeEventListener('submit', handleSubmit);

                // Add the event listener
                replyForm.addEventListener('submit', handleSubmit);

                // Mark that we've added the listener
                listenerAdded = true;
            }
        } else {
            // User is not logged in, show login message
            replyForm.classList.add('hidden');
            replyLoginMessage.classList.remove('hidden');
        }
    });
}

/**
 * Initialize the question page
 */
async function initQuestionPage() {
    const questionId = getQuestionIdFromUrl();

    if (!questionId) {
        showErrorMessage();
        return;
    }

    const questionData = await fetchQuestionData(questionId);

    if (questionData) {
        displayQuestion(questionData);

        // Initialize replies section
        await displayReplies(questionId);

        // Initialize reply form
        initReplyForm(questionId);

        // Initialize content overlay for anonymous users
        contentOverlay.init('.question-container');
    } else {
        showErrorMessage();
    }
}

// Initialize the page when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initQuestionPage);
