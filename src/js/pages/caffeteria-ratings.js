import { auth } from '/js/core/firebase_config.js';


// Function to update UI based on auth state
function updateUIForAuthState(user) {
  const submitButton = document.querySelector('.publish');
  if (submitButton) {
    submitButton.disabled = false;
  }
}

// Listen for auth state changes
auth.onAuthStateChanged(user => {
  console.log("Auth state changed:", user ? `User: ${user.uid}` : "No user");
  updateUIForAuthState(user);
});

// Function to add a rating
async function addRating(rating) {
  // User is already verified to be logged in at this point
  const user = auth.currentUser;
  if (!user) {
    console.error("No user found in addRating function");
    return { success: false, message: "Authentication error" };
  }
  const userId = user.uid;

  console.log("Submitting rating with userId:", userId);

  try {
    // Log the request payload for debugging
    const payload = { rating, userId };
    console.log("Request payload:", payload);

    const response = await fetch('https://api-epv42bk6oq-uc.a.run.app/api/meal/addRating', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });

    console.log("Response status:", response.status);

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Server error:", errorData);
      return { success: false, message: errorData.message || "Server error" };
    }

    const data = await response.json();
    return data;
  } catch (err) {
    console.error("addRating error:", err);
    return { success: false, message: "Server error. Please try again." };
  }
}

// Function to fetch meals
async function fetchMeals() {
  try {
    const response = await fetch('https://api-epv42bk6oq-uc.a.run.app/api/meal/meals');
    if (!response.ok) {
      throw new Error('Failed to fetch meals');
    }

    const data = await response.json();
    return data.meals;
  } catch (error) {
    console.error('Error fetching meals:', error);
    throw error;
  }
}

// Initialize the page
async function initPage() {
  // Initial UI update based on current auth state
  updateUIForAuthState(auth.currentUser);

  const loadingScreen = document.getElementById('loading-screen');
  const mealsContainer = document.getElementById('meals-container');
  const mealsList = document.querySelector('.meals-list');

  try {
    const meals = await fetchMeals();

    // Simplified meal items without extra layers
    mealsList.innerHTML = Object.entries(meals).map(([name, imageUrl]) => `
      <div class="meal-item">
        <img src="${imageUrl}" alt="${name}" class="meal-image">
        <h3>${name}</h3>
      </div>
    `).join('');

    loadingScreen.style.display = 'none';
    mealsContainer.style.display = 'block';
    document.getElementById('reviewField').style.display = 'block';
  } catch (error) {
    loadingScreen.textContent = 'Failed to load meals. Please try again later.';
  }
}

// Set up star rating functionality
function setupStarRating() {
  let selectedRating = null;

  // Function to update star display
  const updateStars = (rating) => {
    document.querySelectorAll('.star').forEach((s, i) => {
      s.textContent = i < rating ? '★' : '☆';
    });
  };

  // Add click event listeners to stars
  document.querySelectorAll('.star').forEach((star) => {
    star.addEventListener('click', () => {
      selectedRating = parseInt(star.getAttribute('data-index'));
      updateStars(selectedRating);
    });
  });

  // Return an object with getter and reset functions
  return {
    getValue: () => selectedRating,
    reset: () => {
      selectedRating = null;
      updateStars(0);
    }
  };
}

// Set up submit button handler
function setupSubmitButton(ratingManager) {
  document.querySelector('.publish').addEventListener('click', async () => {
    // First check if user is logged in
    const user = auth.currentUser;
    if (!user) {
      // If not logged in, show message and redirect
      alert('You need to login');
      window.location.href = "/pages/auth/login.html";
      return;
    }

    // Log user info for debugging
    console.log("Current user:", user);
    console.log("Provider data:", user.providerData);

    // Continue with rating submission if logged in
    const selectedRating = ratingManager.getValue();
    if (!selectedRating) {
      alert('Lütfen bir puan seçin!');
      return;
    }

    const submitButton = document.querySelector('.publish');
    submitButton.disabled = true;
    submitButton.textContent = 'Submitting...';

    try {
      console.log("Selected rating:", selectedRating);
      const result = await addRating(selectedRating);
      console.log("Rating result:", result);

      if (result.success) {
        submitButton.textContent = 'Submitted!';
        setTimeout(() => {
          submitButton.textContent = 'Submit';
          submitButton.disabled = false;
          // Reset stars
          ratingManager.reset();
        }, 2000);
      } else {
        // Show the exact error message from the server
        console.error("Rating submission failed:", result);
        alert(result.message || "Rating submission failed");
        submitButton.textContent = 'Submit';
        submitButton.disabled = false;
      }
    } catch (error) {
      console.error("Error submitting rating:", error);
      alert(error.message || 'Rating submission failed');
      submitButton.textContent = 'Submit';
      submitButton.disabled = false;
    }
  });
}

// Initialize everything when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  initPage();
  const ratingManager = setupStarRating();
  setupSubmitButton(ratingManager);
});
