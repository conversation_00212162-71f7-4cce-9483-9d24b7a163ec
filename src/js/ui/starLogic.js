const stars = document.querySelectorAll(".star");

var selectedRating = null;

stars.forEach((star, index) => {
    star.addEventListener("click", () => {
        if (selectedRating === index + 1) {
            selectedRating = null;
            stars.forEach((s) => {
                s.textContent = "☆";
            });
        } else {
            selectedRating = index + 1;
            stars.forEach((s, i) => {
                s.textContent = i < selectedRating ? "★" : "☆";
            });
        }
        console.log("Rated:", selectedRating);
    });
});

export default selectedRating;