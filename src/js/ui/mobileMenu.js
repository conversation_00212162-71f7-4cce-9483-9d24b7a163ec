document.addEventListener('DOMContentLoaded', () => {
    const hamburgerMenu = document.querySelector('.hamburger-menu'); // Select by class
    const mobileMenu = document.querySelector('.mobile-menu'); // Select by class

    // Check if elements exist before adding listener
    if (hamburgerMenu && mobileMenu) {
        hamburgerMenu.addEventListener('click', (event) => {
            event.stopPropagation(); // Prevent click from immediately closing the menu
            mobileMenu.classList.toggle('active');
        });
        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            // Check         if the click is outside the mobile menu and not         on the hamburger itself
            if (!mobileMenu.contains(e.target) && !hamburgerMenu.contains(e.target)) {
                mobileMenu.classList.remove('active');
            }
        });
    } else {
        console.error('Hamburger menu or mobile menu element not found!');
    }
});