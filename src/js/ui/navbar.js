import { auth } from "/js/core/firebase_config.js";
import { onAuthStateChanged } from "https://www.gstatic.com/firebasejs/9.21.0/firebase-auth.js";
import { initNotificationUI } from "/js/components/notifications.js";

// Variable to track if we've already initialized the auth state
let authInitialized = false;

// Function to handle authentication state changes
function handleAuthState(user) {
    // Select all profile-related elements
    const profileLinks = document.querySelectorAll('.profile-button, .profile-icon, .nav-link[href="/pages/profile/profile.html"]');

    // Select all login buttons
    const loginButtons = document.querySelectorAll('.login-button');

    if (user) {
        // User is logged in
        console.log('User is logged in, updating UI');

        // Update profile links to include the user's ID
        profileLinks.forEach(link => {
            link.href = `/pages/profile/profile.html?userId=${user.uid}`;

            // Remove any click event listeners that might have been added
            const newLink = link.cloneNode(true);
            link.parentNode.replaceChild(newLink, link);
        });

        // Also update the profile link in the mobile menu
        const mobileProfileLink = document.querySelector('.mobile-menu .nav-menu a.nav-link:nth-child(4)');
        if (mobileProfileLink && mobileProfileLink.textContent.trim() === 'Profile') {
            mobileProfileLink.href = `/pages/profile/profile.html?userId=${user.uid}`;
        }

        // Hide all login buttons when user is logged in
        loginButtons.forEach(button => {
            button.classList.remove('show');
            // Use setTimeout to ensure the animation has time to complete
            setTimeout(() => {
                button.style.display = 'none';
            }, 300);
        });
    } else {
        // User is not logged in
        console.log('User is not logged in, updating UI');

        // Redirect to login page when profile links are clicked
        profileLinks.forEach(link => {
            // Remove the href attribute to prevent default navigation
            link.removeAttribute('href');

            // Add click event listener to redirect to login page
            link.addEventListener('click', (e) => {
                e.preventDefault();
                window.location.href = '/pages/auth/login.html';
            });
        });

        // Also update the profile link in the mobile menu
        const mobileProfileLink = document.querySelector('.mobile-menu .nav-menu a.nav-link:nth-child(4)');
        if (mobileProfileLink && mobileProfileLink.textContent.trim() === 'Profile') {
            mobileProfileLink.removeAttribute('href');
            mobileProfileLink.addEventListener('click', (e) => {
                e.preventDefault();
                window.location.href = '/pages/auth/login.html';
            });
        }

        // Show all login buttons when user is not logged in
        loginButtons.forEach(button => {
            button.style.display = 'block';
            // Add a small delay before showing to ensure the display property is applied
            setTimeout(() => {
                button.classList.add('show');
            }, 10);

            // Add click event listener to login buttons
            button.addEventListener('click', () => {
                window.location.href = '/pages/auth/login.html';
            });
        });
    }

    // Mark that we've initialized the auth state
    authInitialized = true;
}

async function loadHeader() {
    try {
        // Check if we already have the auth state before loading the navbar
        const currentUser = auth.currentUser;

        const response = await fetch('/components/navbar.html');
        const html = await response.text();
        document.getElementById('navbar').innerHTML = html;

        // If we already know the auth state, apply it immediately
        if (currentUser) {
            handleAuthState(currentUser);
        }

        // Set up the auth state change listener for future changes
        onAuthStateChanged(auth, (user) => {
            handleAuthState(user);
        });

        // Initialize notification system
        initNotificationUI();

        // Add hamburger menu functionality
        const hamburgerMenu = document.querySelector('.hamburger-menu');
        const mobileMenu = document.querySelector('.mobile-menu');
        const closeMenuBtn = document.querySelector('.close-menu');
        const menuOverlay = document.querySelector('.menu-overlay');

        // Function to toggle mobile menu
        const toggleMobileMenu = () => {
            hamburgerMenu.classList.toggle('active');
            mobileMenu.classList.toggle('active');
            document.body.classList.toggle('menu-open'); // Prevent scrolling when menu is open
        };

        if (hamburgerMenu && mobileMenu) {
            // Open menu when hamburger is clicked
            hamburgerMenu.addEventListener('click', toggleMobileMenu);

            // Close menu when close button is clicked
            if (closeMenuBtn) {
                closeMenuBtn.addEventListener('click', toggleMobileMenu);
            }

            // Close menu when clicking on a link or profile button
            const mobileLinks = mobileMenu.querySelectorAll('a.nav-link, a.profile-button');
            mobileLinks.forEach(link => {
                // Only add the toggle menu event if it's not the profile link without href
                // (which means it's for a non-logged-in user)
                if (link.hasAttribute('href') || !link.classList.contains('profile-button')) {
                    link.addEventListener('click', toggleMobileMenu);
                } else if (link.classList.contains('profile-button')) {
                    // For profile links without href (non-logged-in users),
                    // add both the login redirect and toggle menu functionality
                    link.addEventListener('click', () => {
                        toggleMobileMenu();
                        // The redirect to login page is already handled by the auth state listener
                    });
                }
            });

            // Close menu when clicking on the overlay
            if (menuOverlay) {
                menuOverlay.addEventListener('click', toggleMobileMenu);
            }
        }
    } catch (error) {
        console.error('Error loading navbar:', error);
    }
}

document.addEventListener("DOMContentLoaded", loadHeader);