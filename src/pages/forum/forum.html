<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forum - MetuHub</title>
    <link rel="stylesheet" href="/css/base.css">
    <link rel="stylesheet" href="/css/components/login-button.css">
    <link rel="stylesheet" href="/css/components/profile-button.css">
    <link rel="stylesheet" href="/css/components/notifications.css">
    <link rel="stylesheet" href="/css/pages/forum.css">
    <link rel="stylesheet" href="/css/components/post.css">
    <link rel="stylesheet" href="/css/components/content-overlay.css">
</head>
<body>
    <div id="navbar"></div>
    <main>
        <div class="forum-navbar">
            <div class="forum-actions">
                <a href="/pages/forum/create-question.html" class="ask-question-button">Ask Question</a>
            </div>
        </div>
        <div class="questions-list" id="questionsList">
            <div class="simple-loading" id="loading-indicator">
                <span class="loading-text">Loading questions...</span>
                <div class="loading-spinner"></div>
            </div>
        </div>
    </main>

    <script type="module" src="/js/ui/navbar.js"></script>
    <script type="module">
        import { renderPosts } from '/js/components/post.js';
        import { contentOverlay } from '/js/utils/contentOverlay.js';
        import { createInfiniteScroll } from '/js/services/infiniteScrollPosts.js';

        // Global variables
        let infiniteScroll;
        let questionsList;
        let loadingIndicator;

        /**
         * Initialize the forum page with infinite scroll
         */
        async function initializeForum() {
            try {
                // Get DOM elements
                questionsList = document.getElementById('questionsList');
                loadingIndicator = document.getElementById('loading-indicator');

                if (!questionsList) {
                    throw new Error('Questions list container not found');
                }

                // Create loading indicator for infinite scroll
                const infiniteLoadingIndicator = createInfiniteScrollLoadingIndicator();

                // Initialize infinite scroll
                infiniteScroll = createInfiniteScroll({
                    container: questionsList,
                    loadingIndicator: infiniteLoadingIndicator,
                    limit: 15,
                    rootMargin: '200px', // Start loading 200px before reaching bottom
                    onPostsLoaded: handlePostsLoaded,
                    onError: handleLoadingError
                });

                // Start infinite scroll
                await infiniteScroll.init();

            } catch (error) {
                console.error('Error initializing forum:', error);
                showErrorMessage('Unable to load the forum. Please refresh the page or try again later.');
            }
        }

        /**
         * Create loading indicator for infinite scroll
         */
        function createInfiniteScrollLoadingIndicator() {
            const indicator = document.createElement('div');
            indicator.className = 'infinite-loading-indicator';
            indicator.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <span class="loading-text">Loading more posts...</span>
                </div>
            `;
            indicator.style.display = 'none';
            questionsList.appendChild(indicator);
            return indicator;
        }

        /**
         * Handle posts loaded callback
         */
        async function handlePostsLoaded(posts, isFirstPage, metadata) {
            try {
                console.log(`Posts loaded: ${posts.length}, First page: ${isFirstPage}, Total: ${metadata.totalPosts}`);

                if (isFirstPage) {
                    // Remove initial loading indicator
                    if (loadingIndicator) {
                        loadingIndicator.remove();
                        loadingIndicator = null;
                    }

                    // Clear container for first page
                    const existingPosts = questionsList.querySelectorAll('.post-item');
                    existingPosts.forEach(post => post.remove());

                    if (posts.length === 0) {
                        showEmptyState();
                        return;
                    }
                }

                if (posts.length > 0) {
                    // Create container for new posts
                    const postsContainer = document.createElement('div');
                    postsContainer.className = 'posts-batch';

                    // Render posts
                    await renderPosts(posts, postsContainer, '/pages/forum/question.html', false);

                    // Insert posts before the loading indicator
                    const loadingIndicator = questionsList.querySelector('.infinite-loading-indicator');
                    if (loadingIndicator) {
                        questionsList.insertBefore(postsContainer, loadingIndicator);
                    } else {
                        questionsList.appendChild(postsContainer);
                    }

                    // Initialize content overlay for anonymous users (only on first page)
                    if (isFirstPage) {
                        contentOverlay.init('.questions-list');
                    }
                }

                // Show end message if no more posts
                if (!metadata.hasMore && !questionsList.querySelector('.end-of-posts')) {
                    showEndOfPosts();
                }

            } catch (error) {
                console.error('Error handling loaded posts:', error);
                handleLoadingError(error);
            }
        }

        /**
         * Handle loading errors
         */
        function handleLoadingError(error) {
            console.error('Loading error:', error);

            // Remove initial loading indicator if present
            if (loadingIndicator) {
                loadingIndicator.remove();
                loadingIndicator = null;
            }

            // Show error message
            showErrorMessage('Unable to load posts. Please refresh the page or try again later.');
        }

        /**
         * Show empty state when no posts are available
         */
        function showEmptyState() {
            const emptyState = document.createElement('div');
            emptyState.className = 'empty-state';
            emptyState.innerHTML = `
                <p>No questions have been posted yet.</p>
                <p>Be the first to ask a question!</p>
                <a href="/pages/forum/create-question.html" class="ask-question-button">Ask Question</a>
            `;
            questionsList.appendChild(emptyState);
        }

        /**
         * Show end of posts message
         */
        function showEndOfPosts() {
            const endMessage = document.createElement('div');
            endMessage.className = 'end-of-posts';
            endMessage.innerHTML = `
                <p>You've reached the end of all posts!</p>
                <a href="#top" onclick="window.scrollTo({top: 0, behavior: 'smooth'})">Back to top</a>
            `;
            questionsList.appendChild(endMessage);
        }

        /**
         * Show error message
         */
        function showErrorMessage(message) {
            questionsList.innerHTML = `
                <div class="error-state">
                    <p>${message}</p>
                    <button onclick="window.location.reload()" class="retry-button">Retry</button>
                </div>
            `;
        }

        /**
         * Cleanup function for page unload
         */
        function cleanup() {
            if (infiniteScroll) {
                infiniteScroll.destroy();
                infiniteScroll = null;
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', initializeForum);

        // Cleanup on page unload
        window.addEventListener('beforeunload', cleanup);
    </script>
</body>
</html>