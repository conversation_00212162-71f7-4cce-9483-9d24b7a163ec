<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forum - MetuHub</title>
    <link rel="stylesheet" href="/css/base.css">
    <link rel="stylesheet" href="/css/components/login-button.css">
    <link rel="stylesheet" href="/css/components/profile-button.css">
    <link rel="stylesheet" href="/css/components/notifications.css">
    <link rel="stylesheet" href="/css/pages/forum.css">
    <link rel="stylesheet" href="/css/components/post.css">
    <link rel="stylesheet" href="/css/components/content-overlay.css">
</head>
<body>
    <div id="navbar"></div>
    <main>
        <div class="forum-navbar">
            <div class="forum-actions">
                <a href="/pages/forum/create-question.html" class="ask-question-button">Ask Question</a>
            </div>
        </div>
        <div class="questions-list" id="questionsList">
            <div class="simple-loading" id="loading-indicator">
                <span class="loading-text">Loading questions...</span>
                <div class="loading-spinner"></div>
            </div>
        </div>
    </main>

    <script type="module" src="/js/ui/navbar.js"></script>
    <script type="module">
        import { db } from '/js/core/firebase_config.js';
        import { collection, query, orderBy, getDocs } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
        import { renderPosts } from '/js/components/post.js';
        import { contentOverlay } from '/js/utils/contentOverlay.js';

        async function loadQuestions() {
            try {
                const questionsRef = collection(db, "questions");
                const q = query(questionsRef, orderBy("createdAt", "desc"));
                const querySnapshot = await getDocs(q);
                const questionsList = document.getElementById('questionsList');
                const loadingIndicator = document.getElementById('loading-indicator');

                if (querySnapshot.empty) {
                    // Remove loading indicator
                    if (loadingIndicator) {
                        loadingIndicator.remove();
                    }

                    questionsList.innerHTML = '<p>No questions have been posted yet. Check back later for new content.</p>';
                    return;
                }

                // Convert Firestore documents to question objects
                const questions = [];
                querySnapshot.forEach((doc) => {
                    const question = doc.data();
                    question.id = doc.id; // Add document ID to the question object
                    questions.push(question);
                });

                // Remove the loading indicator before rendering posts
                if (loadingIndicator) {
                    loadingIndicator.remove();
                }

                // Render the posts using our component
                await renderPosts(questions, questionsList, '/pages/forum/question.html', false); // Pass false to not show the loading overlay

                // Initialize content overlay for anonymous users
                contentOverlay.init('.questions-list');
            } catch (error) {
                console.error("Error loading questions:", error);
                const loadingIndicator = document.getElementById('loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.remove();
                }

                document.getElementById('questionsList').innerHTML =
                    '<p>Unable to load questions at the moment. Please refresh the page or try again later.</p>';
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            loadQuestions();
        });
    </script>
</body>
</html>