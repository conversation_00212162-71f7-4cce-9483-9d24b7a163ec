<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ask Question - MetuHub</title>
    <link rel="stylesheet" href="/css/base.css">
    <link rel="stylesheet" href="/css/components/login-button.css">
    <link rel="stylesheet" href="/css/components/profile-button.css">
    <link rel="stylesheet" href="/css/components/notifications.css">
    <link rel="stylesheet" href="/css/pages/forum.css">
</head>
<body>
    <div id="navbar"></div>
    <main>
        <div class="question-form">
            <h1>Ask a Question</h1>
            <form id="questionForm">
                <div class="form-group">
                    <label for="title">Title</label>
                    <input type="text" id="title" maxlength="150" required>
                    <small>Maximum 150 characters</small>
                </div>
                <div class="form-group">
                    <label for="body">Body</label>
                    <textarea id="body" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="tags">Tags</label>
                    <div class="tags-input" id="tagsContainer">
                        <input type="text" id="tagInput" placeholder="Add up to 5 tags">
                    </div>
                    <small>Press Enter to add a tag (maximum 5 tags)</small>
                </div>
                <button type="submit" class="ask-question-btn">Post Question</button>
            </form>
        </div>
    </main>

    <script type="module" src="/js/ui/navbar.js"></script>
    <script type="module">
        import { auth } from '/js/core/firebase_config.js';

        document.addEventListener('DOMContentLoaded', () => {
            // The navbar will be loaded by the navbar.js script
        });

        const form = document.getElementById('questionForm');
        const tagsContainer = document.getElementById('tagsContainer');
        let tagInput = document.getElementById('tagInput');
        let tags = [];

        // Function to handle tag input
        function handleTagInput(e) {
            if (e.key === 'Enter') {
                e.preventDefault(); // Prevent form submission
                const tag = tagInput.value.trim();
                if (tag && tags.length < 5 && !tags.includes(tag)) {
                    tags.push(tag);
                    updateTagsDisplay();
                }
                tagInput.value = '';
            }
        }

        // Initial event listener
        tagInput.addEventListener('keydown', handleTagInput);

        // Update tags display
        function updateTagsDisplay() {
            const tagElements = tags.map(tag => `
                <span class="tag">${tag}<span class="tag-remove" data-tag="${tag}">&times;</span></span>
            `).join('');
            tagsContainer.innerHTML = tagElements + `<input type="text" id="tagInput" placeholder="Add up to 5 tags">`;

            // Reattach input reference
            tagInput = document.getElementById('tagInput');

            // Reattach the keydown event listener to the new input
            tagInput.addEventListener('keydown', handleTagInput);

            // Add click handlers for remove buttons
            document.querySelectorAll('.tag-remove').forEach(button => {
                button.addEventListener('click', () => {
                    tags = tags.filter(t => t !== button.dataset.tag);
                    updateTagsDisplay();
                });
            });

            // Disable the submit button if the tag input is focused
            tagInput.addEventListener('focus', () => {
                document.querySelector('button[type="submit"]').disabled = true;
            });

            // Enable the submit button when the tag input loses focus
            tagInput.addEventListener('blur', () => {
                document.querySelector('button[type="submit"]').disabled = false;
            });

            // Focus on the input for adding more tags
            tagInput.focus();
        }

        // Handle form submission
        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            if (!auth.currentUser) {
                alert('Please login to post a question');
                return;
            }

            try {
                const response = await fetch('https://api-epv42bk6oq-uc.a.run.app/api/posts/post', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: auth.currentUser.uid,
                        title: document.getElementById('title').value,
                        body: document.getElementById('body').value,
                        tags,
                        createdAt: new Date(),
                        viewCount: 0,
                        replies: []
                    })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to create question');
                }

                window.location.href = 'forum.html';
            } catch (error) {
                alert(error.message);
            }
        });
    </script>
</body>
</html>