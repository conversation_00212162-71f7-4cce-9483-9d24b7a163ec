<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - MetuHub</title>
    <link rel="stylesheet" href="/css/base.css">
    <link rel="stylesheet" href="/css/components/login-button.css">
    <link rel="stylesheet" href="/css/components/profile-button.css">
    <link rel="stylesheet" href="/css/components/notifications.css">
    <link rel="stylesheet" href="/css/pages/forum.css">
    <link rel="stylesheet" href="/css/components/post.css">
    <link rel="stylesheet" href="/css/pages/profile.css">
    <link rel="stylesheet" href="/css/components/content-overlay.css">
</head>
<body>
    <div id="navbar"></div>

    <div class="container">
        <div class="navigation-bar">
            <a href="/pages/forum/forum.html" class="back-button">Back to Forum</a>
        </div>

        <div id="loading" class="loading">Loading profile...</div>

        <div id="profile-content" class="profile-container" style="display: none;">
            <div class="profile-header">
                <h1 class="profile-username" id="profile-username"></h1>
                <div class="profile-actions">
                    <a href="/pages/forum/create-question.html" class="ask-question-button">Ask Question</a>
                </div>
            </div>

            <div class="profile-section">
                <h2 class="profile-section-title">My Questions</h2>
                <div id="questions-count" class="questions-count"></div>
                <div id="user-questions"></div>
                <div id="no-questions" class="no-questions" style="display: none;">
                    You haven't asked any questions yet. <a href="/pages/forum/create-question.html">Ask your first question!</a>
                </div>
            </div>

            <div id="logout-container" class="logout-container" style="display: none;">
                <button id="logout-button" class="logout-button">Sign Out</button>
            </div>
        </div>

        <div id="error-message" class="error-message" style="display: none;">
            <p>You need to be logged in to view your profile.</p>
            <div class="error-actions">
                <a href="/pages/auth/login.html" class="login-link">Login to your account</a>
                <a href="/pages/auth/sign-up.html" class="signup-link">Create an account</a>
            </div>
        </div>
        <!-- TODO: DO not use this login logic. use our login page. If user is not exist-->
    </div>

    <script type="module" src="/js/ui/navbar.js"></script>
    <script type="module" src="/js/pages/profile.js"></script>
</body>
</html>