<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Forgot Password - MetuHub</title>
    <link rel="stylesheet" href="/css/base.css" />
    <link rel="stylesheet" href="/css/components/login-button.css" />
    <link rel="stylesheet" href="/css/components/profile-button.css" />
    <link rel="stylesheet" href="/css/components/notifications.css" />
    <link rel="stylesheet" href="/css/pages/auth.css" />
  </head>
  <body>
    <div id="navbar"></div>
    <div class="auth-container">
      <div class="auth-box">
        <h2>Forgot Password</h2>
        <p class="verification-message">Enter your METU email address and we'll send you a link to reset your password.</p>
        <form id="forgotPasswordForm">
          <div class="form-group">
            <input
              type="email"
              id="email"
              placeholder="METU Email"
              pattern=".+@metu\.edu\.tr$"
              required
            />
            <div class="error-message" id="emailError">
              Please enter a valid @metu.edu.tr email
            </div>
          </div>
          <button type="submit" id="submitButton" class="submit-btn">Send Reset Link</button>
          <div class="resend-message" id="statusMessage"></div>
        </form>
        <div class="login-link">
          <a href="login.html">Back to Login</a>
        </div>
      </div>
    </div>

    <script type="module" src="/js/ui/navbar.js"></script>
    <script type="module">
      document.addEventListener('DOMContentLoaded', () => {
        if (typeof loadHeader === 'function') {
          loadHeader();
        }
      });

      const form = document.getElementById("forgotPasswordForm");
      const emailInput = document.getElementById("email");
      const emailError = document.getElementById("emailError");
      const submitButton = document.getElementById("submitButton");
      const statusMessage = document.getElementById("statusMessage");

      // Validate email format
      emailInput.addEventListener("input", () => {
        if (!emailInput.value.endsWith("@metu.edu.tr")) {
          emailError.style.display = "block";
        } else {
          emailError.style.display = "none";
        }
      });

      // Handle form submission
      form.addEventListener("submit", async (e) => {
        e.preventDefault();

        const email = emailInput.value;

        // Reset error messages
        emailError.style.display = "none";
        statusMessage.textContent = "";
        statusMessage.className = "resend-message";

        // Validate email format
        if (!email.endsWith("@metu.edu.tr")) {
          emailError.style.display = "block";
          return;
        }

        try {
          // Disable submit button and show loading state
          submitButton.disabled = true;
          submitButton.textContent = "Sending...";

          // Send request to the API
          const response = await fetch("https://api-epv42bk6oq-uc.a.run.app/api/auth/forgot-password", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email })
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.error || "Failed to send reset link");
          }

          // Show success message
          statusMessage.textContent = "Password reset link has been sent to your email. Please check your inbox.";
          statusMessage.className = "resend-message success";

          // Clear the form
          form.reset();

        } catch (error) {
          console.error("Error sending reset link:", error);

          // Show error message
          statusMessage.textContent = error.message || "Failed to send reset link. Please try again.";
          statusMessage.className = "resend-message error";
        } finally {
          // Re-enable submit button
          submitButton.disabled = false;
          submitButton.textContent = "Send Reset Link";
        }
      });
    </script>
  </body>
</html>
