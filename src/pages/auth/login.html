<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - MetuHub</title>
    <link rel="stylesheet" href="/css/base.css" />
    <link rel="stylesheet" href="/css/components/login-button.css" />
    <link rel="stylesheet" href="/css/components/profile-button.css" />
    <link rel="stylesheet" href="/css/components/notifications.css" />
    <link rel="stylesheet" href="/css/pages/auth.css" />
  </head>
  <body>
    <div id="navbar"></div>
    <div class="auth-container">
      <div class="auth-box">
        <h2>Login</h2>
        <form id="loginForm">
          <div class="form-group">
            <input
              type="email"
              id="email"
              placeholder="METU Email"
              pattern=".+@metu\.edu\.tr$"
              required
            />
            <div class="error-message" id="emailError">
              Please enter a valid @metu.edu.tr email
            </div>
          </div>
          <div class="form-group">
            <input
              type="password"
              id="password"
              placeholder="Password"
              required
            />
          </div>
          <button type="submit" id="loginButton" class="submit-btn">Login</button>
          <div class="error-message" id="loginError" style="display: none;">
            Invalid email or password
          </div>
        </form>
        <div class="forgot-password-link">
          <a href="forgot-password.html">Forgot Password?</a>
        </div>
        <div class="login-link">
          Don't have an account? <a href="sign-up.html">Sign Up</a>
        </div>
      </div>
    </div>

    <script type="module" src="/js/ui/navbar.js"></script>
    <script type="module">
      import { auth } from "/js/core/firebase_config.js";
      import { signInWithEmailAndPassword } from "https://www.gstatic.com/firebasejs/9.21.0/firebase-auth.js";

      document.addEventListener('DOMContentLoaded', () => {
        loadHeader();
      });

      const form = document.getElementById("loginForm");
      const emailInput = document.getElementById("email");
      const emailError = document.getElementById("emailError");

      emailInput.addEventListener("input", () => {
        if (!emailInput.value.endsWith("@metu.edu.tr")) {
          emailError.style.display = "block";
        } else {
          emailError.style.display = "none";
        }
      });

      form.addEventListener("submit", async (e) => {
        e.preventDefault();

        const email = emailInput.value;
        const password = document.getElementById("password").value;
        const loginButton = document.getElementById("loginButton");
        const loginError = document.getElementById("loginError");

        loginError.style.display = "none";
        emailError.style.display = "none";

        if (!email.endsWith("@metu.edu.tr")) {
          emailError.style.display = "block";
          return;
        }

        try {
          loginButton.disabled = true;
          loginButton.textContent = "Logging in...";

          const userCredential = await signInWithEmailAndPassword(auth, email, password);
          const user = userCredential.user;

          if (!user.emailVerified) {
            loginError.textContent = "Please verify your email before logging in.";
            loginError.style.display = "block";
            loginButton.disabled = false;
            loginButton.textContent = "Login";
            return;
          }

          const idToken = await user.getIdToken();

          const response = await fetch("https://api-epv42bk6oq-uc.a.run.app/api/auth/login", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${idToken}`,
            }
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.error || "Login failed");
          }

          window.location.href = "/pages/forum/forum.html";
        } catch (error) {
          console.error("Login error:", error);
          let errorMessage = "Invalid email or password";

          if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
            errorMessage = "Invalid email or password";
          } else if (error.code === 'auth/too-many-requests') {
            errorMessage = "Too many failed attempts. Please try again later.";
          } else if (error.message) {
            errorMessage = error.message;
          }

          loginError.textContent = errorMessage;
          loginError.style.display = "block";
        } finally {
          loginButton.disabled = false;
          loginButton.textContent = "Login";
        }
      });
    </script>
  </body>
</html>