<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - MetuHub</title>
    <link rel="stylesheet" href="/css/base.css">
    <link rel="stylesheet" href="/css/components/login-button.css">
    <link rel="stylesheet" href="/css/components/profile-button.css">
    <link rel="stylesheet" href="/css/components/notifications.css">
    <link rel="stylesheet" href="/css/pages/auth.css">
</head>
<body>
    <div id="navbar"></div>
    <div class="auth-container">
        <div class="auth-box">
            <h2>Verify Your Email</h2>
            <div class="verification-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#d32f2f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
            </div>
            <p class="verification-message">We've sent a verification link to your email address. Please check your inbox and click the link to verify your account.</p>
            <p class="verification-note">After verification, you can <a href="login.html">login here</a>.</p>
            <div class="resend-container">
                <p>Didn't receive the email?</p>
                <button id="resendButton" class="submit-btn">Resend Verification Email</button>
                <div id="resendMessage" class="resend-message"></div>
            </div>
        </div>
    </div>

    <script type="module" src="/js/ui/navbar.js"></script>
    <script type="module">
        import { auth } from '/js/core/firebase_config.js';
        import { sendEmailVerification } from "https://www.gstatic.com/firebasejs/9.21.0/firebase-auth.js";

        document.addEventListener('DOMContentLoaded', async () => {
            loadHeader();

            // Check if the user is already verified
            try {
                const currentUser = auth.currentUser;
                if (currentUser) {
                    const idToken = await currentUser.getIdToken();

                    // Check verification status from backend
                    const response = await fetch('https://api-epv42bk6oq-uc.a.run.app/api/auth/user', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${idToken}`
                        }
                    });

                    if (response.ok) {
                        const userData = await response.json();
                        if (userData.isVerified) {
                            // User is already verified, show message and redirect
                            const resendMessage = document.getElementById('resendMessage');
                            resendMessage.textContent = 'Your account is already verified! Redirecting to login...';
                            resendMessage.className = 'resend-message success';

                            // Hide the resend button
                            document.getElementById('resendButton').style.display = 'none';

                            // Update the verification message
                            document.querySelector('.verification-message').textContent =
                                'Your email has been verified successfully!';

                            // Update the verification note
                            document.querySelector('.verification-note').innerHTML =
                                'You will be redirected to the login page in a few seconds. Or <a href="login.html">click here</a> to login now.';

                            // Redirect to login after a delay
                            setTimeout(() => {
                                window.location.href = 'login.html';
                            }, 3000);
                        }
                    }
                }
            } catch (error) {
                console.error('Error checking verification status:', error);
            }
        });

        const resendButton = document.getElementById('resendButton');
        const resendMessage = document.getElementById('resendMessage');

        resendButton.addEventListener('click', async () => {
            try {
                resendButton.disabled = true;
                resendButton.textContent = 'Sending...';
                resendMessage.textContent = '';
                resendMessage.className = 'resend-message';

                const currentUser = auth.currentUser;
                if (!currentUser || !currentUser.email) {
                    resendMessage.textContent = 'Please sign in again to resend verification email.';
                    resendMessage.className = 'resend-message error';
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                    return;
                }

                // First try to send verification directly through Firebase
                try {
                    await sendEmailVerification(currentUser);
                    resendMessage.textContent = 'Verification email sent successfully! Please check your inbox.';
                    resendMessage.className = 'resend-message success';
                    return;
                } catch (firebaseError) {
                    console.log('Firebase verification failed, trying backend:', firebaseError);
                    // If Firebase direct method fails, try through backend
                }

                // Fallback to backend verification endpoint
                const response = await fetch('https://api-epv42bk6oq-uc.a.run.app/api/auth/verification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: currentUser.email,
                        uid: currentUser.uid
                    })
                });

                if (response.ok) {
                    resendMessage.textContent = 'Verification email sent successfully! Please check your inbox.';
                    resendMessage.className = 'resend-message success';
                } else {
                    const error = await response.json();
                    resendMessage.textContent = error.message || 'Failed to send verification email. Please try again.';
                    resendMessage.className = 'resend-message error';
                }
            } catch (error) {
                console.error('Error:', error);
                resendMessage.textContent = 'Failed to send verification email. Please try again.';
                resendMessage.className = 'resend-message error';
            } finally {
                resendButton.disabled = false;
                resendButton.textContent = 'Resend Verification Email';
            }
        });
    </script>
</body>
</html>