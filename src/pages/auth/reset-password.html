<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reset Password - MetuHub</title>
    <link rel="stylesheet" href="/css/base.css" />
    <link rel="stylesheet" href="/css/components/login-button.css" />
    <link rel="stylesheet" href="/css/components/profile-button.css" />
    <link rel="stylesheet" href="/css/components/notifications.css" />
    <link rel="stylesheet" href="/css/pages/auth.css" />
  </head>
  <body>
    <div id="navbar"></div>
    <div class="auth-container">
      <div class="auth-box">
        <h2>Reset Password</h2>
        <p class="verification-message">Enter your new password below.</p>
        <form id="resetPasswordForm">
          <div class="form-group">
            <input
              type="password"
              id="password"
              placeholder="New Password"
              required
              minlength="6"
            />
            <div class="input-hint">Password must be at least 6 characters</div>
          </div>
          <div class="form-group">
            <input
              type="password"
              id="confirmPassword"
              placeholder="Confirm Password"
              required
            />
            <div class="error-message" id="passwordError">
              Passwords do not match
            </div>
          </div>
          <button type="submit" id="submitButton" class="submit-btn">Reset Password</button>
          <div class="resend-message" id="statusMessage"></div>
        </form>
        <div class="login-link">
          <a href="login.html">Back to Login</a>
        </div>
      </div>
    </div>

    <script type="module" src="/js/ui/navbar.js"></script>
    <script type="module">
      document.addEventListener('DOMContentLoaded', () => {
        if (typeof loadHeader === 'function') {
          loadHeader();
        }

        // Check if we have a token in the URL
        checkForToken();
      });

      const form = document.getElementById("resetPasswordForm");
      const passwordInput = document.getElementById("password");
      const confirmPasswordInput = document.getElementById("confirmPassword");
      const passwordError = document.getElementById("passwordError");
      const submitButton = document.getElementById("submitButton");
      const statusMessage = document.getElementById("statusMessage");

      // Store the token from URL
      let resetToken = '';

      // Function to get token from URL
      function checkForToken() {
        const urlParams = new URLSearchParams(window.location.search);
        resetToken = urlParams.get('token');

        if (!resetToken) {
          // No token found, show error and disable form
          statusMessage.textContent = "Invalid or missing reset token. Please request a new password reset link.";
          statusMessage.className = "resend-message error";
          form.querySelectorAll('input, button').forEach(el => el.disabled = true);
        }
      }

      // Validate password match
      confirmPasswordInput.addEventListener("input", validatePasswords);
      passwordInput.addEventListener("input", validatePasswords);

      function validatePasswords() {
        if (passwordInput.value !== confirmPasswordInput.value) {
          passwordError.style.display = "block";
          return false;
        } else {
          passwordError.style.display = "none";
          return true;
        }
      }

      // Handle form submission
      form.addEventListener("submit", async (e) => {
        e.preventDefault();

        // Reset error messages
        passwordError.style.display = "none";
        statusMessage.textContent = "";
        statusMessage.className = "resend-message";

        // Validate passwords match
        if (!validatePasswords()) {
          return;
        }

        // Validate password length
        if (passwordInput.value.length < 6) {
          statusMessage.textContent = "Password must be at least 6 characters";
          statusMessage.className = "resend-message error";
          return;
        }

        try {
          // Disable submit button and show loading state
          submitButton.disabled = true;
          submitButton.textContent = "Resetting...";

          // Send request to the API
          const response = await fetch("https://api-epv42bk6oq-uc.a.run.app/api/auth/reset-password", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              token: resetToken,
              password: passwordInput.value
            })
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.error || "Failed to reset password");
          }

          // Show success message
          statusMessage.textContent = "Password has been reset successfully!";
          statusMessage.className = "resend-message success";

          // Redirect to login page after a delay
          setTimeout(() => {
            window.location.href = "login.html";
          }, 3000);

        } catch (error) {
          console.error("Error resetting password:", error);

          // Show error message
          statusMessage.textContent = error.message || "Failed to reset password. Please try again.";
          statusMessage.className = "resend-message error";
        } finally {
          // Re-enable submit button
          submitButton.disabled = false;
          submitButton.textContent = "Reset Password";
        }
      });
    </script>
  </body>
</html>
