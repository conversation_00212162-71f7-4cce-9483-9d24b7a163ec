<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sign Up - MetuHub</title>
    <link rel="stylesheet" href="/css/base.css" />
    <link rel="stylesheet" href="/css/components/login-button.css" />
    <link rel="stylesheet" href="/css/components/profile-button.css" />
    <link rel="stylesheet" href="/css/components/notifications.css" />
    <link rel="stylesheet" href="/css/pages/auth.css" />
  </head>
  <body>
    <div id="navbar"></div>
    <div class="auth-container">
      <div class="auth-box">
        <h2>Sign Up</h2>
        <form id="signupForm">
          <div class="form-group">
            <input
              type="email"
              id="email"
              placeholder="METU Email"
              pattern=".+@metu\.edu\.tr$"
              required
            />
            <div class="error-message" id="emailError">
              Please enter a valid @metu.edu.tr email
            </div>
          </div>
          <div class="form-group">
            <input
              type="text"
              id="username"
              placeholder="Username"
              required
              minlength="3"
              maxlength="20"
            />
            <div class="input-hint">
              Choose a unique username (3-20 characters, letters, numbers, and underscores only)
            </div>
            <div class="error-message" id="usernameError">
              Username must be 3-20 characters and contain only letters, numbers, and underscores
            </div>
          </div>
          <div class="form-group">
            <input
              type="password"
              id="password"
              placeholder="Password"
              required
            />
          </div>
          <div class="form-group">
            <input
              type="password"
              id="confirmPassword"
              placeholder="Confirm Password"
              required
            />
            <div class="error-message" id="passwordError">
              Passwords do not match
            </div>
          </div>
          <button type="submit" id="submitButton" class="submit-btn">Sign Up</button>
        </form>
        <div class="login-link">
          Already have an account? <a href="login.html">Login</a>
        </div>
      </div>
    </div>

    <script type="module" src="/js/ui/navbar.js"></script>
    <script type="module">
      import { signup } from "/js/auth/auth.js";

      document.addEventListener('DOMContentLoaded', () => {
        loadHeader();
      });

      const form = document.getElementById("signupForm");
      const emailInput = document.getElementById("email");
      const emailError = document.getElementById("emailError");
      const usernameInput = document.getElementById("username");
      const usernameError = document.getElementById("usernameError");
      const passwordInput = document.getElementById("password");
      const confirmPasswordInput = document.getElementById("confirmPassword");
      const passwordError = document.getElementById("passwordError");

      // Username validation function
      function validateUsername(username) {
        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
        return usernameRegex.test(username);
      }

      emailInput.addEventListener("input", () => {
        if (!emailInput.value.endsWith("@metu.edu.tr")) {
          emailError.style.display = "block";
          emailInput.classList.add("error");
        } else {
          emailError.style.display = "none";
          emailInput.classList.remove("error");
        }
      });

      usernameInput.addEventListener("input", () => {
        if (!validateUsername(usernameInput.value)) {
          usernameError.style.display = "block";
          usernameInput.classList.add("error");
        } else {
          usernameError.style.display = "none";
          usernameInput.classList.remove("error");
        }
      });

      confirmPasswordInput.addEventListener("input", () => {
        if (passwordInput.value !== confirmPasswordInput.value) {
          passwordError.style.display = "block";
          confirmPasswordInput.classList.add("error");
        } else {
          passwordError.style.display = "none";
          confirmPasswordInput.classList.remove("error");
        }
      });

      form.addEventListener("submit", async (e) => {
        e.preventDefault();
        let hasError = false;

        // Validate email
        if (!emailInput.value.endsWith("@metu.edu.tr")) {
          emailError.style.display = "block";
          emailInput.classList.add("error");
          hasError = true;
        } else {
          emailError.style.display = "none";
          emailInput.classList.remove("error");
        }

        // Validate username
        if (!validateUsername(usernameInput.value)) {
          usernameError.style.display = "block";
          usernameInput.classList.add("error");
          hasError = true;
        } else {
          usernameError.style.display = "none";
          usernameInput.classList.remove("error");
        }

        // Validate password
        if (passwordInput.value !== confirmPasswordInput.value) {
          passwordError.style.display = "block";
          confirmPasswordInput.classList.add("error");
          hasError = true;
        } else {
          passwordError.style.display = "none";
          confirmPasswordInput.classList.remove("error");
        }

        if (hasError) {
          return;
        }

        try {
          const submitButton = document.getElementById("submitButton");
          submitButton.disabled = true;
          submitButton.textContent = "Signing up...";

          const data = await signup(emailInput.value, passwordInput.value, usernameInput.value);
          alert(data.message || "Account created successfully! Please verify your email.");
          window.location.href = "verification.html";
        } catch (error) {
          alert("Signup failed: " + error.message);
          const submitButton = document.getElementById("submitButton");
          submitButton.disabled = false;
          submitButton.textContent = "Sign Up";
        }
      });
    </script>
  </body>
</html>
