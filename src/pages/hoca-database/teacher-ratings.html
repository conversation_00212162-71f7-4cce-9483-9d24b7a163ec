<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hoca Database</title>
    <link rel="stylesheet" href="/css/base.css">
    <link rel="stylesheet" href="/css/components/login-button.css">
    <link rel="stylesheet" href="/css/components/profile-button.css">
    <link rel="stylesheet" href="/css/components/notifications.css">
</head>
<body>
    <div id="navbar"></div>

    <div class="container">
        <h1 class="page-title">Teacher Database</h1>
        <div class="actions">
            <a href="rate-teacher.html" class="add-rating-btn">Rate a Teacher</a>
        </div>
        <table class="teacher-table">
            <thead>
                <tr>
                    <th>Course Code</th>
                    <th>Lecturer</th>
                    <th>Grade</th>
                    <th>Rating</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody id="teacherRatingsTableBody">
                <!-- Teacher ratings will be loaded dynamically -->
            </tbody>
        </table>
    </div>

    <script type="module" src="/js/ui/navbar.js"></script>
    <script type="module">
        // Function to load and display teacher ratings from the backend API
        async function loadTeacherRatings() {
            try {
                const response = await fetch('https://api-epv42bk6oq-uc.a.run.app/api/lecturers/lecturerRating');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const ratings = await response.json();
                const tableBody = document.getElementById('teacherRatingsTableBody');
                tableBody.innerHTML = '';

                ratings.forEach((data) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td data-label="Course Code">${data.courseCode || 'N/A'}</td>
                        <td data-label="Lecturer">${data.lecturer || 'N/A'}</td>
                        <td data-label="Grade">${data.grade || 'N/A'}</td>
                        <td data-label="Rating">${data.rating || 'N/A'}</td>
                        <td data-label="Description">${data.description || 'N/A'}</td>
                    `;
                    tableBody.appendChild(row);
                });
            } catch (error) {
                console.error('Error loading ratings from backend:', error);
                const tableBody = document.getElementById('teacherRatingsTableBody');
                tableBody.innerHTML = '<tr><td colspan="5">Failed to load ratings. Please try again later.</td></tr>';
            }
        }

        window.addEventListener('load', () => {
            loadTeacherRatings();
        });
    </script>
</body>
</html>