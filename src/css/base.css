.rating-container {
    text-align: center;
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

h1,
h2 {
    text-align: center;
}

button {
    display: block;
    margin: 20px auto;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.title {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
}

.date {
    text-align: center;
    font-size: 14px;
    color: #bbb;
    margin-top: 5px;
}

.appbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background-color: #222;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    width: 100%;
    box-sizing: border-box;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 24px;
}

.nav-link {
    color: #fff;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    transition: background-color 0.3s;
    font-size: 14px;
    font-weight: 500;
}

.logo {
    font-size: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
}

.logo span {
    margin-left: 4px;
    padding: 4px 8px;
    background-color: #d32f2f;
    border-radius: 4px;
    color: #ffffff;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    background-color: #d32f2f;
}

.stars {
    text-align: center;
    margin: 8;
}

.star {
    font-size: 40px;
    cursor: pointer;
}

.star.filled {
    color: #ffc107;
}

.centered-box-container {
    display: flex;
    justify-content: center;
}

.publish {
    background-color: #4CAF50;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.publish:hover {
    background-color: #45a049;
}

.publish:disabled::before {
    display: block;
    color: black;
}

.ratings-histogram {
    align-items: center;
    display: flex;
    color: #000;
    font-size: 16px;
    margin-bottom: 10px;
}

.histogram {
    display: flex;
    align-items: flex-end;
    height: 60px;
    width: 180px;
    margin: 0 auto;
    background: #2a2a2a;
    padding: 5px;
    border-radius: 5px;
}

.bar {
    background-color: #5a5a5a;
    border-radius: 3px;
    transition: height 0.3s ease-in-out;
}

.average {
    justify-content: center;
    display: flex;
    font-size: 20px;
    font-weight: bold;
    margin-top: 10px;
    margin: 0;
    padding: 0;
}

.average span:last-child {
    margin-left: 8px;
}

.meal-slider-container {
    width: 100%;
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: center;
}


.meal-slider {
    display: flex;
    overflow-x: auto;
    gap: 15px;
    scroll-behavior: smooth;
}

.meal-item img {
    width: 150px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.meal-item p {
    margin-top: 10px;
    font-size: 14px;
    color: #333;
}

#loading-screen {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f0f0f0;
    font-size: 1.5rem;
}

.hidden {
    display: none;
}

.teacher-table {
    width: 100%;
    max-width: 1000px;
    margin: 20px auto;
    border-collapse: collapse;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.teacher-table th,
.teacher-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.teacher-table th {
    background-color: #222;
    color: white;
    font-weight: bold;
}

.teacher-table tr:hover {
    background-color: #f5f5f5;
}

.teacher-table td:first-child {
    font-weight: bold;
    color: #d32f2f;
}

.container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.page-title {
    text-align: center;
    color: #222;
    font-size: 24px;
}

.actions {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.add-rating-btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #d32f2f;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    text-align: center;
}

.add-rating-btn:hover {
    background-color: #b71c1c;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.2);
}

.mobile-actions {
    display: none;
    align-items: center;
    gap: 10px;
}

.hamburger-menu {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    cursor: pointer;
    padding: 10px;
    height: 24px;
    width: 30px;
}

.hamburger-menu span {
    display: block;
    width: 100%;
    height: 3px;
    background-color: white;
    margin: 0;
    transition: 0.4s;
}

/* Hamburger menu animation when active */
.hamburger-menu.active span:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
}

.hamburger-menu.active span:nth-child(2) {
    opacity: 0;
}

.hamburger-menu.active span:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
}

/* Prevent body scrolling when menu is open */
body.menu-open {
    overflow: hidden;
}

/* Menu overlay */
.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: -1;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
}

.mobile-menu.active .menu-overlay {
    opacity: 1;
    visibility: visible;
}

.mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background-color: #222;
    padding-top: 60px;
    transition: 0.3s ease-in-out;
    z-index: 999;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
    overflow-y: auto;
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-menu .logo {
    font-size: 18px;
}

.close-menu {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    margin: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s;
}

.close-menu:hover {
    transform: scale(1.1);
}

.mobile-menu .nav-menu {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0;
}

.mobile-menu a {
    display: block;
    width: 100%;
    padding: 12px 15px;
    color: white;
    text-decoration: none;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: background-color 0.2s;
    word-wrap: break-word;
    overflow-wrap: break-word;
    line-height: 1.3;
    cursor: pointer;
}

.mobile-menu a:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.mobile-menu .menu-buttons {
    padding: 15px 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.mobile-menu a.active {
    background-color: #d32f2f;
}

.mobile-only {
    display: none;
}

@media screen and (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .mobile-actions {
        display: flex;
    }

    .hamburger-menu {
        display: flex;
    }

    .mobile-menu {
        display: block;
    }

    .mobile-only {
        display: inline;
    }

    .teacher-table {
        margin: 8px 0 10px 0;
        font-size: 13px;
        border-radius: 0;
        box-shadow: none;
    }

    .teacher-table thead {
        display: none;
    }

    .teacher-table,
    .teacher-table tbody,
    .teacher-table tr,
    .teacher-table td {
        display: block;
        width: 100%;
    }

    .teacher-table tbody {
        padding: 6px;
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .teacher-table tr {
        margin-bottom: 0;
        border: 1px solid #eee;
        border-radius: 6px;
        overflow: hidden;
        /* background-color: white; */
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .teacher-table td {
        padding: 5px 8px;
        text-align: left;
        position: relative;
        padding-left: 100px;
        min-height: 26px;
        border-bottom: 1px solid #eee;
        word-wrap: break-word;
        overflow-wrap: break-word;
        max-width: calc(100% - 100px);
        line-height: 1.2;
        font-size: 13px;
    }

    .teacher-table td:before {
        content: attr(data-label);
        position: absolute;
        left: 10px;
        /* width: 90px; */
        font-weight: bold;
        color: #222;
        /* font-size: 13px; */
    }

    .container {
        padding: 8px;
        margin: 0;
    }

    .page-title {
        font-size: 18px;
        margin: 10px 0 5px 0;
        padding: 0 10px;
        background-color: white;
    }

    .actions {
        margin-bottom: 8px;
        padding: 0 10px;
    }

    .add-rating-btn {
        width: 90%;
        box-sizing: border-box;
    }
}

.rating-form {
    max-width: 500px;
    width: 90%;
    margin: 0 auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #444;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    background-color: #ffffff;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #d32f2f;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1);
}

.form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.submit-btn {
    width: 100%;
    padding: 10px;
    background-color: #d32f2f;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 12px;
}

.submit-btn:hover {
    background-color: #b71c1c;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.2);
}

.submit-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

.meals-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
}

.meal-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.meal-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 10px;
}

.meal-item h3 {
    margin: 10px 0;
    color: #333;
    font-size: 16px;
}

.meal-item p {
    margin: 5px 0;
    color: #666;
}