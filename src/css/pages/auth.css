.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
}

.auth-box {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.form-group input:focus {
    border-color: #d32f2f;
    outline: none;
}

.form-group input.error {
    border-color: #d32f2f;
    background-color: rgba(211, 47, 47, 0.05);
}

.form-group .input-hint {
    color: #666;
    font-size: 12px;
    margin-top: 4px;
}

/* Verification page styles */
.verification-icon {
    text-align: center;
    margin: 20px 0;
}

.verification-message {
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 20px;
    text-align: center;
    color: #555;
}

.verification-note {
    font-size: 14px;
    margin-bottom: 30px;
    text-align: center;
}

.verification-note a {
    color: #d32f2f;
    text-decoration: none;
    font-weight: 500;
}

.verification-note a:hover {
    text-decoration: underline;
}

.resend-container {
    text-align: center;
    margin-top: 20px;
}

.resend-container p {
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.resend-message {
    margin-top: 10px;
    font-size: 14px;
    padding: 10px;
    border-radius: 4px;
    display: none;
}

.resend-message:not(:empty) {
    display: block;
}

.resend-message.success {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
}

.resend-message.error {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
}

.error-message {
    color: #d32f2f;
    font-size: 14px;
    margin-top: 5px;
    display: none;
}

.submit-btn {
    width: 100%;
    padding: 12px;
    background: #d32f2f;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
}

.submit-btn:hover {
    background: #b71c1c;
    transform: translateY(-1px);
}

.submit-btn:disabled {
    background: #aaa;
    cursor: not-allowed;
    transform: none;
}

.forgot-password-link {
    text-align: right;
    margin-top: 10px;
    font-size: 14px;
}

.forgot-password-link a {
    color: #d32f2f;
    text-decoration: none;
}

.forgot-password-link a:hover {
    text-decoration: underline;
}

.login-link {
    text-align: center;
    margin-top: 20px;
}

.login-link a {
    color: #d32f2f;
    font-weight: bold;
    text-decoration: none;
}