/* Profile Page Styles */

.navigation-bar {
    max-width: 800px;
    margin: 1rem auto;
    padding: 0 1rem;
}

.back-button {
    display: inline-flex;
    align-items: center;
    background-color: #f5f5f5;
    color: #333;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    margin-bottom: 0.8rem;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.back-button:hover {
    background-color: #e0e0e0;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

/* Style for the arrow in the back button */
.back-button .arrow {
    color: #333;
    margin-right: 8px;
}

.profile-container {
    max-width: 800px;
    margin: 1rem auto;
    padding: 1rem;
}

.profile-header {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.profile-actions {
    text-align: right;
    flex-shrink: 0;
}

.ask-question-button {
    display: inline-block;
    padding: 0.3rem 0.7rem;
    background-color: #d32f2f;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    text-decoration: none;
    cursor: pointer;
    font-weight: 500;
}

.ask-question-button:hover {
    background-color: #b71c1c;
}

.follow-button {
    display: inline-block;
    padding: 0.3rem 0.7rem;
    background-color: #d32f2f;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    text-decoration: none;
    cursor: pointer;
    font-weight: 500;
    margin-right: 8px;
}

.follow-button:hover {
    background-color: #b71c1c;
}

.follow-button.following {
    background-color: #757575;
}

.follow-button.following:hover {
    background-color: #616161;
}

.profile-username {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
    display: block;
}

/* Profile info styles */
.profile-info {
    margin-bottom: 1rem;
}

.profile-info p {
    margin: 0.25rem 0;
    color: #555;
    font-size: 0.9rem;
}

.join-date {
    color: #666;
}

.question-count {
    font-weight: 500;
}

.social-stats {
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: #666;
}

.followers-count, .following-count {
    display: inline-block;
}

.stats-separator {
    margin: 0 0.5rem;
    color: #999;
}

.profile-section {
    margin-bottom: 2rem;
}

.profile-section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.error-message {
    color: #c62828;
    margin: 1rem 0;
    text-align: center;
}

#no-questions {
    display: none;
    text-align: center;
    color: #666;
    margin: 2rem 0;
}

.no-questions-message {
    margin: 1rem 0;
    color: #555;
    text-align: center;
}

/* Questions count */
.questions-count {
    margin-bottom: 1rem;
    color: #666;
    font-size: 0.9rem;
}

/* Posts container */
.posts-container {
    margin-bottom: 1.5rem;
}

/* Load More button */
.load-more-button {
    display: block;
    width: 100%;
    max-width: 200px;
    margin: 1rem auto;
    padding: 0.5rem;
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
}

.load-more-button:hover {
    background-color: #e0e0e0;
}

.load-more-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Go Back to Top button */
.go-back-button {
    display: block;
    width: 100%;
    max-width: 200px;
    margin: 1rem auto;
    padding: 0.5rem;
    background-color: #f0f0f0;
    color: #555;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
}

.go-back-button:hover {
    background-color: #e0e0e0;
}

/* No more questions message */
.no-more-questions-message {
    text-align: center;
    color: #666;
    margin: 1rem 0;
    font-style: italic;
}

/* Logout button styles */
.logout-container {
    text-align: center;
    margin: 2rem 0 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.logout-button {
    display: inline-block;
    padding: 0.5rem 1.2rem;
    background-color: #d32f2f;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    text-decoration: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.logout-button:hover {
    background-color: #b71c1c;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Pagination controls */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.pagination-button {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 0 4px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
    background-color: #e0e0e0;
}

.pagination-button.active {
    background-color: #d32f2f;
    color: white;
    border-color: #d32f2f;
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-pages {
    display: flex;
    align-items: center;
}

.pagination-ellipsis {
    margin: 0 4px;
    color: #666;
}

@media screen and (max-width: 768px) {
    .navigation-bar {
        margin: 0.5rem auto;
        padding: 0 0.5rem;
    }

    .back-button {
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
        margin-bottom: 0.5rem;
    }

    .profile-container {
        padding: 0.5rem;
        margin: 0.5rem;
    }

    .profile-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .profile-username {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .profile-info {
        margin-bottom: 1rem;
    }

    .social-stats {
        margin-top: 0.25rem;
    }

    .profile-actions {
        text-align: left;
        margin-top: 0.5rem;
        margin-bottom: 1rem;
        width: 100%;
    }

    .ask-question-button, .follow-button, .logout-button {
        width: auto;
        text-align: center;
        padding: 0.25rem 0.6rem;
        font-size: 0.75rem;
    }

    .logout-container {
        margin: 1.5rem 0 0.5rem;
        padding-top: 0.75rem;
    }
}
