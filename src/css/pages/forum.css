.questions-list {
    max-width: 900px;
    margin: 2rem auto;
    padding: 0 1rem;
    position: relative;
    min-height: 400px;
}

.forum-navbar {
    max-width: 900px;
    margin: 2rem auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.forum-actions {
    text-align: right;
}

.ask-question-button {
    display: inline-block;
    padding: 0.3rem 0.7rem;
    background-color: #d32f2f;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    text-decoration: none;
    cursor: pointer;
    font-weight: 500;
}

.ask-question-button:hover {
    background-color: #b71c1c;
}


.question-item {
    display: flex;
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    gap: 1rem;
}

.question-stats {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    min-width: 100px;
}

.stat {
    text-align: center;
}

.stat .number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #555;
}

.stat .label {
    font-size: 0.8rem;
    color: #666;
}

.question-content {
    flex: 1;
}

.question-title {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    color: #1202a2;
    text-decoration: none;
    text-align: left;
    padding-left: 0;
    margin-left: 0;
}

.question-content {
    flex: 1;
    text-align: left;
    padding-left: 0;
}

.question-title:hover {
    color: #1202a2;
}

.question-excerpt {
    color: #444;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.question-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.tags {
    display: flex;
    gap: 0.5rem;
}

.tag {
    background: #e1ecf4;
    color: #39739d;
    padding: 0.3rem 0.6rem;
    border-radius: 3px;
    font-size: 0.8rem;
}

.post-info {
    color: #666;
}

.question-form {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.question-form .form-group {
    margin-bottom: 20px;
}

.question-form input,
.question-form textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Specific styles for the body textarea */
.question-form textarea#body {
    resize: vertical;
    min-height: 80px;
    max-height: 300px;
}

.tags-input {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 5px;
}

.tag {
    background: #e0e0e0;
    padding: 5px 10px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.tag-remove {
    cursor: pointer;
    color: #666;
}

/* Post Question button */
.ask-question-btn {
    background-color: #d32f2f;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 1rem;
}

.ask-question-btn:hover {
    background-color: #b71c1c;
}

.ask-question-btn:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
}

/* Simple loading indicator */
.simple-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 0;
    text-align: center;
    width: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: transparent;
}

.loading-text {
    margin-bottom: 15px;
    color: #666;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #ddd;
    border-top-color: #d32f2f;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}