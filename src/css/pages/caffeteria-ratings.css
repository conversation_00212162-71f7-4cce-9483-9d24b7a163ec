/* Cafeteria Ratings Page Styles - Simplified */

.content-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 10px;
  text-align: center;
}

#loading-screen {
  text-align: center;
  padding: 20px;
  font-size: 16px;
  color: #666;
}

/* Meals display */
.meals-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  justify-content: center;
  gap: 15px;
  margin: 20px auto;
  max-width: 800px;
}

.meal-item {
  margin-bottom: 10px;
  text-align: center;
}

.meal-image {
  width: 100%;
  height: 140px;
  object-fit: cover;
}

.meal-item h3 {
  margin: 5px 0;
  font-size: 14px;
  text-align: center;
}

/* Rating container */
.review-container {
  margin: 20px auto;
  text-align: center;
}

.rating-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

/* Stars styling */
.stars {
  display: flex;
  gap: 8px;
  font-size: 28px;
  color: #ffc107;
  cursor: pointer;
  justify-content: center;
  margin: 0 auto;
}

/* Submit button */
.publish {
  background-color: #d32f2f;
  color: white;
  border: none;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  margin: 10px auto;
  display: block;
}

.publish:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* Histogram container */
#histogram-container {
  margin: 20px auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .meals-list {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }

  .stars {
    font-size: 24px;
  }
}
