.navigation-bar {
    max-width: 700px;
    margin: 1rem auto;
    padding: 0 1rem;
}

.back-button {
    display: inline-flex;
    align-items: center;
    background-color: #f5f5f5;
    color: #333;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    margin-bottom: 0.8rem;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.back-button:hover {
    background-color: #e0e0e0;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.back-button {
    /* Arrow is now included directly in HTML */
}

/* Style for the arrow in the back button */
.back-button span.arrow {
    margin-right: 8px;
    font-size: 1rem;
    color: #333;
}

.question-container {
    max-width: 700px;
    width: 95%;
    margin: 1rem auto 2rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1.25rem;
    box-sizing: border-box;
    overflow: hidden;
}

.question-title {
    font-size: 1.3rem;
    color: #1202a2;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.question-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
}

.question-date {
    font-size: 0.85rem;
    color: #666;
    text-align: right;
    margin-left: 10px;
    white-space: nowrap;
}

.question-body {
    font-size: 0.95rem;
    line-height: 1.5;
    color: #333;
    margin-bottom: 1rem;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    max-width: 100%;
}

.question-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    flex: 1;
}

.tag {
    background: #e1ecf4;
    color: #39739d;
    padding: 0.3rem 0.6rem;
    border-radius: 3px;
    font-size: 0.8rem;
}

.error-message {
    text-align: center;
    padding: 2rem;
}

.back-link {
    display: inline-flex;
    align-items: center;
    background-color: #f5f5f5;
    color: #333;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    margin-top: 1rem;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.back-link:hover {
    background-color: #e0e0e0;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.back-link {
    /* Arrow is now included directly in HTML */
}

/* Style for the arrow in the back link */
.back-link span.arrow {
    margin-right: 8px;
    font-size: 1rem;
    color: #333;
}

.hidden {
    display: none;
}

/* Additional styles for the replies section */
.replies-section {
    margin-top: 2rem;
}

.replies-title {
    display: flex;
    align-items: center;
}

.replies-count {
    font-size: 0.9rem;
    font-weight: normal;
    color: #666;
    margin-left: 5px;
}

@media (max-width: 768px) {
    .replies-title {
        font-size: 1rem;
    }

    .replies-count {
        font-size: 0.8rem;
        margin-left: 4px;
    }
}

.reply-submit-btn {
    background-color: #d32f2f;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.reply-submit-btn:hover {
    background-color: #b71c1c;
}

@media (max-width: 768px) {
    .navigation-bar {
        margin: 0.5rem auto;
        padding: 0 0.5rem;
    }

    .back-button {
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
        margin-bottom: 0.5rem;
    }

    .question-container {
        padding: 0.75rem;
        margin: 0.5rem auto;
        border-radius: 6px;
        max-width: 100%;
        width: 95%;
    }

    .question-title {
        font-size: 1.2rem;
    }

    .question-body {
        font-size: 0.95rem;
    }

    .question-footer {
        flex-direction: row;
        align-items: center;
    }

    .question-date {
        font-size: 0.75rem;
        margin-left: 8px;
    }

    .tag {
        font-size: 0.75rem;
        padding: 0.2rem 0.5rem;
    }

    /* Mobile styles for question container */
    .question-container {
        margin-bottom: 1rem;
    }

    /* Note: Detailed mobile styles for replies are now in replies.css */
}
