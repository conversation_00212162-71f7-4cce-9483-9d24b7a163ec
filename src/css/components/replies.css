/* Replies Component Styles */

.replies-section {
    margin-top: 1.5rem;
    border-top: 1px solid #e0e0e0;
    padding-top: 1rem;
}

.replies-title {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    color: #333;
}

.replies-container {
    margin-bottom: 1rem;
}

.reply-item {
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
}

.reply-content {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    color: #333;
    white-space: pre-wrap;
    max-width: 100%;
}

.reply-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #666;
}

.reply-author {
    font-weight: 600;
}

.reply-author .username-link {
    color: #444;
    text-decoration: none;
    cursor: pointer;
}

.reply-author .username-link:hover {
    color: #d32f2f;
    text-decoration: underline;
}

.reply-date {
    font-size: 0.75rem;
}

.replies-loading {
    text-align: center;
    padding: 0.5rem;
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
}

.no-replies-message {
    text-align: center;
    padding: 0.5rem;
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
}

.hidden {
    display: none;
}

/* Reply Form Styles */
.reply-form-container {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 0.75rem;
    margin-top: 0.75rem;
}

.reply-form-title {
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.reply-form {
    display: flex;
    flex-direction: column;
}

.reply-form textarea.reply-content {
    width: 100%;
    min-height: 60px;
    max-height: 150px;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    font-family: inherit;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    box-sizing: border-box;
}

.reply-submit-btn {
    align-self: flex-end;
    background-color: #d32f2f;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.reply-submit-btn:hover {
    background-color: #b71c1c;
}

.reply-login-message {
    text-align: center;
    padding: 1rem;
    color: #666;
}

.reply-login-message a {
    color: #d32f2f;
    text-decoration: none;
    font-weight: 600;
}

.reply-login-message a:hover {
    text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .replies-section {
        margin-top: 1rem;
        padding-top: 0.75rem;
    }

    .reply-item {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .reply-content {
        font-size: 0.85rem;
        line-height: 1.3;
        margin-bottom: 0.4rem;
    }

    .reply-form-container {
        padding: 0.5rem;
        margin-top: 0.5rem;
    }

    .reply-form textarea.reply-content {
        min-height: 50px;
        padding: 0.4rem;
        font-size: 0.85rem;
    }

    .reply-form-title {
        font-size: 0.9rem;
        margin-bottom: 0.4rem;
    }

    .reply-submit-btn {
        padding: 0.3rem 0.7rem;
        font-size: 0.8rem;
    }

    .reply-footer {
        font-size: 0.75rem;
    }

    .reply-date {
        font-size: 0.7rem;
    }

    .replies-loading,
    .no-replies-message,
    .reply-login-message {
        padding: 0.4rem;
        font-size: 0.85rem;
    }

    .reply-login-message a {
        font-size: 0.85rem;
    }
}
