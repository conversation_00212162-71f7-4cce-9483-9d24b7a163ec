.post-item {
    display: block;
    padding: 0.75rem 0.9rem;
    border-bottom: 1px solid #e0e0e0;
    background-color: white;
    border-radius: 4px;
    margin-bottom: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    width: 100%;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
    height: auto;
    min-height: 0;
}

.post-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.post-content {
    width: 100%;
}

.post-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: #1202a2;
    text-decoration: none;
    text-align: left;
    font-weight: 500;
    line-height: 1.3;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.post-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
}

.tags {
    display: flex;
    gap: 0.4rem;
    flex-wrap: wrap;
    flex: 1;
}

.tag {
    background: #e1ecf4;
    color: #39739d;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.75rem;
}

.post-info {
    color: #666;
    font-size: 0.75rem;
    text-align: right;
    margin-left: 10px;
    white-space: nowrap;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.post-meta {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.post-stats {
    margin-top: 4px;
}

.post-replies {
    font-size: 0.75rem;
    color: #666;
    background-color: #f5f5f5;
    padding: 2px 6px;
    border-radius: 10px;
    display: inline-block;
}

.post-username {
    font-weight: 600;
    margin-bottom: 2px;
    position: relative;
}

/* Username link styling */
.username-link {
    color: #444;
    text-decoration: none;
    cursor: pointer;
}

.username-link:hover {
    color: #d32f2f;
    text-decoration: underline;
}

/* Add a small dot separator between username and date */
.post-username::after {
    content: "•";
    display: inline-block;
    margin: 0 4px;
    color: #999;
    font-weight: normal;
}

/* Loading indicator */
.posts-loading {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-size: 1rem;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin: 1rem auto;
    max-width: 800px;
    position: relative;
}

.posts-loading::after {
    content: "";
    display: inline-block;
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid #ddd;
    border-top-color: #d32f2f;
    border-radius: 50%;
    margin-left: 0.5rem;
    vertical-align: middle;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive styles */
@media (max-width: 768px) {
    .post-item {
        padding: 0.6rem 0.75rem;
        max-width: 100%;
    }

    .post-title {
        font-size: 1rem;
    }

    .post-footer {
        flex-direction: row;
        align-items: center;
    }

    .post-info {
        font-size: 0.7rem;
        margin-left: 8px;
    }

    .post-meta {
        font-size: 0.7rem;
    }

    .post-username {
        font-size: 0.7rem;
        margin-bottom: 0;
    }

    .post-stats {
        margin-top: 3px;
    }

    .post-replies {
        font-size: 0.7rem;
        padding: 1px 5px;
    }
}
