/* Notification System Styles */

/* Notification Icon */
.notification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    padding: 8px;
    margin-right: 10px;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

/* Specific styling for desktop navbar */
.appbar .nav-menu .notification-icon {
    padding: 8px 12px;
}

.notification-icon:hover {
    transform: translateY(-1px);
}

/* Bell icon using SVG as background */
.notification-icon::before {
    content: "";
    display: inline-block;
    width: 18px;
    height: 18px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath fill='white' d='M224 0c-17.7 0-32 14.3-32 32V51.2C119 66 64 130.6 64 208v18.8c0 47-17.3 92.4-48.5 127.6l-7.4 8.3c-8.4 9.4-10.4 22.9-5.3 34.4S19.4 416 32 416H416c12.6 0 24-7.4 29.2-18.9s3.1-25-5.3-34.4l-7.4-8.3C401.3 319.2 384 273.9 384 226.8V208c0-77.4-55-142-128-156.8V32c0-17.7-14.3-32-32-32zm45.3 493.3c12-12 18.7-28.3 18.7-45.3H160c0 17 6.7 33.3 18.7 45.3s28.3 18.7 45.3 18.7s33.3-6.7 45.3-18.7z'%3E%3C/path%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* Red dot indicator for unread notifications */
.notification-icon.has-unread::after {
    content: "";
    position: absolute;
    top: 4px;
    right: 4px;
    width: 8px;
    height: 8px;
    background-color: #d32f2f;
    border-radius: 50%;
    border: 1px solid #222;
}

/* Notification Dropdown */
.notification-dropdown {
    position: fixed;
    top: 60px;
    /* Default position, will be overridden by JavaScript */
    left: 50%;
    transform: translateX(-50%);
    width: 320px;
    max-height: 400px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999; /* Increased z-index to ensure it appears above all other elements */
    overflow: hidden;
    display: none;
    flex-direction: column;
    /* Ensure the dropdown doesn't go off-screen */
    max-width: calc(100vw - 20px);
}

.notification-dropdown.active {
    display: flex;
}

.notification-header {
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #333;
}

.notification-list {
    overflow-y: auto;
    max-height: 320px;
    padding: 0;
    margin: 0;
    list-style: none;
}

.notification-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    flex-direction: column;
}

.notification-item:hover {
    background-color: #f9f9f9;
}

.notification-item.unread {
    background-color: #f0f7ff;
    font-weight: 500;
}

.notification-item.unread:hover {
    background-color: #e6f2ff;
}

.notification-content {
    font-size: 0.85rem;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.4;
}

.notification-time {
    font-size: 0.75rem;
    color: #666;
    align-self: flex-end;
}

.notification-footer {
    padding: 0;
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    text-align: center;
}

.mark-all-read {
    background-color: transparent;
    border: none;
    color: #d32f2f;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    padding: 2px 0;
    width: 100%;
    border-radius: 0;
    transition: background-color 0.2s;
}

.mark-all-read:hover {
    background-color: rgba(211, 47, 47, 0.1);
}

.no-notifications {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
}

/* Mobile Styles */
@media screen and (max-width: 768px) {
    .notification-dropdown {
        top: 60px;
        /* Position will be set by JavaScript */
        width: calc(100% - 20px);
        max-width: 320px;
    }

    /* Adjust mobile notification icon */
    .mobile-actions .notification-icon {
        margin-right: 5px;
    }

    /* Ensure the notification icon is properly aligned with other mobile icons */
    .mobile-actions .notification-icon::before {
        width: 18px;
        height: 18px;
    }
}
