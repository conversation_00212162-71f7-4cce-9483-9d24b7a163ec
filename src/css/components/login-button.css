/* Initially hide login buttons until auth state is determined */
.login-button {
    width: 90%;
    display: none; /* Hide by default */
    text-align: center;
    padding: 10px 16px;
    background-color: #d32f2f;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
    margin: 0;
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
}

/* Style for the login button in the main navbar */
.appbar .login-button {
    width: auto;
    margin: 0;
}

/* Show login button when explicitly set for non-authenticated users */
.login-button.show {
    display: block;
    opacity: 1;
}

/* Animation for smooth appearance */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.login-button:hover {
    background-color: #b71c1c;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.2);
}