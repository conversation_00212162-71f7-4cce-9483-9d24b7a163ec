/* Content Preview Overlay for Anonymous Users */

.content-preview-overlay {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 35vh;
    background: linear-gradient(
        to top,
        rgba(0, 0, 0, 0.9) 0%,
        rgba(0, 0, 0, 0.7) 40%,
        rgba(0, 0, 0, 0.4) 70%,
        rgba(0, 0, 0, 0.1) 90%,
        transparent 100%
    );
    z-index: 1000;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    padding: 1.5rem;
    box-sizing: border-box;
    pointer-events: none;
}

.content-preview-overlay.active {
    pointer-events: all;
}

.overlay-content {
    text-align: center;
    color: white;
    max-width: 400px;
    margin-bottom: 1.5rem;
    pointer-events: all;
}

.overlay-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.overlay-message {
    display: none; /* Hide the message text as requested */
}

.overlay-cta-button {
    display: inline-block;
    padding: 0.75rem 2rem;
    background-color: #d32f2f;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

.overlay-cta-button:hover {
    background-color: #b71c1c;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(211, 47, 47, 0.4);
}

.overlay-cta-button:active {
    transform: translateY(0);
}

/* Content blur effect for posts below overlay */
.content-blurred {
    filter: blur(2px);
    opacity: 0.6;
    pointer-events: none;
    user-select: none;
}

.content-blurred .post-item {
    cursor: default !important;
    pointer-events: none;
}

.content-blurred .post-item:hover {
    transform: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

/* Special handling for question detail pages */
.question-container.content-blurred {
    filter: blur(1px);
    opacity: 0.7;
}

.question-container.content-blurred .question-title,
.question-container.content-blurred .question-body,
.question-container.content-blurred .question-footer {
    pointer-events: none;
    user-select: none;
}

/* Disable interactions for anonymous users */
.anonymous-mode .post-item {
    cursor: default !important;
    pointer-events: none;
}

.anonymous-mode .post-item:hover {
    transform: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.anonymous-mode .username-link {
    pointer-events: none;
    cursor: default;
    color: inherit;
    text-decoration: none;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .content-preview-overlay {
        height: 25vh;
        padding: 1rem;
    }

    .overlay-title {
        font-size: 1.2rem;
        margin-bottom: 0.8rem;
    }

    .overlay-cta-button {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
    }

    .overlay-content {
        margin-bottom: 1rem;
    }
}

/* Tablet responsive styles */
@media (max-width: 1024px) and (min-width: 769px) {
    .content-preview-overlay {
        height: 30vh;
        padding: 1.3rem;
    }

    .overlay-title {
        font-size: 1.25rem;
    }
}

/* Hide overlay for authenticated users */
.authenticated .content-preview-overlay {
    display: none !important;
}

/* Ensure overlay doesn't interfere with navbar */
.content-preview-overlay {
    top: auto;
    bottom: 0;
}

/* Animation for overlay appearance */
.content-preview-overlay {
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.5s ease-out;
}

.content-preview-overlay.show {
    opacity: 1;
    transform: translateY(0);
}

/* Gradient fade effect for content */
.content-fade-mask {
    position: relative;
}

.content-fade-mask::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(
        to top,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0.8) 30%,
        rgba(255, 255, 255, 0.4) 60%,
        rgba(255, 255, 255, 0.1) 80%,
        transparent 100%
    );
    pointer-events: none;
    z-index: 10;
}

/* Ensure overlay appears above all content */
.content-preview-overlay {
    z-index: 9999;
}

/* Prevent scrolling when overlay is active */
body.overlay-active {
    overflow: hidden;
}

/* Additional styles for better visual hierarchy */
.content-preview-overlay .overlay-content {
    /* Removed backdrop-filter and background for cleaner look */
}

/* Improve button accessibility */
.overlay-cta-button:focus {
    outline: 2px solid #fff;
    outline-offset: 2px;
}

/* Better mobile experience */
@media (max-width: 480px) {
    .content-preview-overlay {
        height: 20vh;
        padding: 0.8rem;
    }

    .overlay-content {
        margin-bottom: 0.8rem;
    }

    .overlay-title {
        font-size: 1.1rem;
        margin-bottom: 0.6rem;
    }

    .overlay-cta-button {
        padding: 0.5rem 1.2rem;
        font-size: 0.85rem;
    }
}
