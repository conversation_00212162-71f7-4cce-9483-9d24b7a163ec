<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MetuHub</title>
    <link rel="stylesheet" href="css/base.css" />
    <link rel="stylesheet" href="css/components/login-button.css" />
    <link rel="stylesheet" href="css/components/profile-button.css" />
    <link rel="stylesheet" href="css/components/notifications.css" />
    <script>
      // Use a more reliable way to handle redirection
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          window.location.replace('pages/forum/forum.html');
        }, 100);
      });
    </script>
  </head>
  <body>
    <div id="loading-screen">Redirecting to home screen...</div>
  </body>
</html>