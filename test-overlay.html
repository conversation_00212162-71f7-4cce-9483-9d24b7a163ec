<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Overlay Test - MetuHub</title>
    <link rel="stylesheet" href="/css/base.css">
    <link rel="stylesheet" href="/css/components/post.css">
    <link rel="stylesheet" href="/css/components/content-overlay.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-posts {
            margin: 20px 0;
        }
        .test-post {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-controls {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-controls button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        .test-controls button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Content Overlay Test</h1>

        <div class="test-controls">
            <h3>Test Controls</h3>
            <button onclick="testShowOverlay()">Show Overlay</button>
            <button onclick="testHideOverlay()">Hide Overlay</button>
            <button onclick="testAuthenticatedMode()">Simulate Authenticated</button>
            <button onclick="testAnonymousMode()">Simulate Anonymous</button>
            <button onclick="testDestroy()">Destroy Overlay</button>
        </div>

        <div class="questions-list test-posts">
            <div class="post-item">
                <div class="post-content">
                    <h2 class="post-title">Test Question 1</h2>
                    <div class="post-footer">
                        <div class="tags">
                            <span class="tag">test</span>
                            <span class="tag">overlay</span>
                        </div>
                        <div class="post-info">
                            <div class="post-meta">
                                <span class="post-username">Test User</span>
                                <span>2 days ago</span>
                            </div>
                            <div class="post-stats">
                                <span class="post-replies">3 replies</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="post-item">
                <div class="post-content">
                    <h2 class="post-title">Test Question 2</h2>
                    <div class="post-footer">
                        <div class="tags">
                            <span class="tag">javascript</span>
                            <span class="tag">frontend</span>
                        </div>
                        <div class="post-info">
                            <div class="post-meta">
                                <span class="post-username">Another User</span>
                                <span>1 day ago</span>
                            </div>
                            <div class="post-stats">
                                <span class="post-replies">1 reply</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="post-item">
                <div class="post-content">
                    <h2 class="post-title">Test Question 3</h2>
                    <div class="post-footer">
                        <div class="tags">
                            <span class="tag">css</span>
                            <span class="tag">design</span>
                        </div>
                        <div class="post-info">
                            <div class="post-meta">
                                <span class="post-username">Design User</span>
                                <span>3 hours ago</span>
                            </div>
                            <div class="post-stats">
                                <span class="post-replies">0 replies</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="post-item">
                <div class="post-content">
                    <h2 class="post-title">Test Question 4 (Should be blurred)</h2>
                    <div class="post-footer">
                        <div class="tags">
                            <span class="tag">backend</span>
                            <span class="tag">api</span>
                        </div>
                        <div class="post-info">
                            <div class="post-meta">
                                <span class="post-username">Backend Dev</span>
                                <span>5 hours ago</span>
                            </div>
                            <div class="post-stats">
                                <span class="post-replies">2 replies</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="status" style="margin: 20px 0; padding: 10px; background: #e9ecef; border-radius: 4px;">
            Status: Ready for testing - Overlay should be thinner now with just title and button
        </div>
    </div>

    <script type="module">
        import { ContentOverlay } from '/js/utils/contentOverlay.js';
        import { auth } from '/js/core/firebase_config.js';
        import { onAuthStateChanged } from "https://www.gstatic.com/firebasejs/9.21.0/firebase-auth.js";

        // Create test overlay instance
        window.testOverlay = new ContentOverlay();

        // Monitor auth state for testing
        onAuthStateChanged(auth, (user) => {
            const statusEl = document.getElementById('status');
            if (user) {
                statusEl.textContent = `Status: User logged in (${user.uid}) - Overlay should be hidden`;
                statusEl.style.backgroundColor = '#d4edda';
                statusEl.style.color = '#155724';
            } else {
                statusEl.textContent = 'Status: User not logged in - Overlay should be visible';
                statusEl.style.backgroundColor = '#f8d7da';
                statusEl.style.color = '#721c24';
            }
        });

        // Test functions
        window.testShowOverlay = function() {
            window.testOverlay.init('.questions-list');
            window.testOverlay.isAuthenticated = false;
            window.testOverlay.showOverlay();
            document.getElementById('status').textContent = 'Status: Overlay shown (anonymous mode)';
        };

        window.testHideOverlay = function() {
            window.testOverlay.hideOverlay();
            document.getElementById('status').textContent = 'Status: Overlay hidden';
        };

        window.testAuthenticatedMode = function() {
            window.testOverlay.isAuthenticated = true;
            document.body.classList.add('authenticated');
            document.body.classList.remove('anonymous-mode');
            window.testOverlay.hideOverlay();
            document.getElementById('status').textContent = 'Status: Authenticated mode (overlay should be hidden)';
        };

        window.testAnonymousMode = function() {
            window.testOverlay.isAuthenticated = false;
            document.body.classList.remove('authenticated');
            document.body.classList.add('anonymous-mode');
            window.testOverlay.showOverlay();
            document.getElementById('status').textContent = 'Status: Anonymous mode (overlay should be visible)';
        };

        window.testDestroy = function() {
            window.testOverlay.destroy();
            document.getElementById('status').textContent = 'Status: Overlay destroyed and cleaned up';
        };

        // Initialize in anonymous mode for testing
        document.addEventListener('DOMContentLoaded', () => {
            window.testOverlay.init('.questions-list');
            document.getElementById('status').textContent = 'Status: Initialized - Click buttons to test functionality';
        });
    </script>
</body>
</html>
